"""
A.T.L.A.S RL Order Execution Optimizer
DQN agent for optimal order execution timing to minimize market impact and slippage
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import json
import pickle
import os
from collections import deque
import random

# Try to import RL libraries, fallback to simple execution if not available
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import Dense
    from tensorflow.keras.optimizers import Adam
    RL_AVAILABLE = True
except ImportError:
    RL_AVAILABLE = False

from config import settings
from atlas_performance_optimizer import performance_optimizer


@dataclass
class ExecutionState:
    """Order execution state for RL agent"""
    order_size: float
    fill_percentage: float
    recent_price_impact: float
    bid_ask_spread: float
    volume_rate: float
    time_remaining: float
    market_volatility: float
    
    def to_array(self) -> np.ndarray:
        """Convert state to numpy array for RL model"""
        return np.array([
            self.order_size,
            self.fill_percentage,
            self.recent_price_impact,
            self.bid_ask_spread,
            self.volume_rate,
            self.time_remaining,
            self.market_volatility
        ])


@dataclass
class ExecutionAction:
    """Order execution action"""
    fraction_to_execute: float  # 0.1 to 1.0
    order_type: str  # 'market', 'limit', 'twap'
    urgency: str  # 'low', 'medium', 'high'
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'fraction_to_execute': self.fraction_to_execute,
            'order_type': self.order_type,
            'urgency': self.urgency
        }


@dataclass
class ExecutionResult:
    """Result of order execution"""
    symbol: str
    executed_quantity: float
    average_price: float
    slippage: float
    market_impact: float
    vwap_benchmark: float
    execution_time: float
    success: bool
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'executed_quantity': self.executed_quantity,
            'average_price': self.average_price,
            'slippage': self.slippage,
            'market_impact': self.market_impact,
            'vwap_benchmark': self.vwap_benchmark,
            'execution_time': self.execution_time,
            'success': self.success,
            'timestamp': self.timestamp.isoformat()
        }


class DQNAgent:
    """Deep Q-Network agent for order execution optimization"""
    
    def __init__(self, state_size: int, action_size: int):
        self.state_size = state_size
        self.action_size = action_size
        self.memory = deque(maxlen=10000)
        self.epsilon = 1.0  # Exploration rate
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.learning_rate = 0.001
        self.gamma = 0.95  # Discount factor
        
        # Neural networks
        self.q_network = self._build_model() if RL_AVAILABLE else None
        self.target_network = self._build_model() if RL_AVAILABLE else None
        
        if RL_AVAILABLE and self.q_network and self.target_network:
            self.update_target_network()
    
    def _build_model(self):
        """Build neural network for DQN"""
        try:
            model = Sequential([
                Dense(64, activation='relu', input_shape=(self.state_size,)),
                Dense(64, activation='relu'),
                Dense(32, activation='relu'),
                Dense(self.action_size, activation='linear')
            ])
            
            model.compile(optimizer=Adam(learning_rate=self.learning_rate), loss='mse')
            return model
            
        except Exception as e:
            logging.error(f"Error building DQN model: {e}")
            return None
    
    def update_target_network(self):
        """Update target network weights"""
        if self.q_network and self.target_network:
            self.target_network.set_weights(self.q_network.get_weights())
    
    def remember(self, state, action, reward, next_state, done):
        """Store experience in replay memory"""
        self.memory.append((state, action, reward, next_state, done))
    
    def act(self, state):
        """Choose action using epsilon-greedy policy"""
        if not RL_AVAILABLE or not self.q_network:
            # Fallback to simple action selection
            return random.randint(0, self.action_size - 1)
        
        if np.random.random() <= self.epsilon:
            return random.randint(0, self.action_size - 1)
        
        q_values = self.q_network.predict(state.reshape(1, -1), verbose=0)
        return np.argmax(q_values[0])
    
    def replay(self, batch_size=32):
        """Train the model on a batch of experiences"""
        if not RL_AVAILABLE or not self.q_network or len(self.memory) < batch_size:
            return
        
        try:
            batch = random.sample(self.memory, batch_size)
            states = np.array([e[0] for e in batch])
            actions = np.array([e[1] for e in batch])
            rewards = np.array([e[2] for e in batch])
            next_states = np.array([e[3] for e in batch])
            dones = np.array([e[4] for e in batch])
            
            # Current Q values
            current_q_values = self.q_network.predict(states, verbose=0)
            
            # Next Q values from target network
            next_q_values = self.target_network.predict(next_states, verbose=0)
            
            # Update Q values
            for i in range(batch_size):
                if dones[i]:
                    current_q_values[i][actions[i]] = rewards[i]
                else:
                    current_q_values[i][actions[i]] = rewards[i] + self.gamma * np.max(next_q_values[i])
            
            # Train the model
            self.q_network.fit(states, current_q_values, epochs=1, verbose=0)
            
            # Decay epsilon
            if self.epsilon > self.epsilon_min:
                self.epsilon *= self.epsilon_decay
                
        except Exception as e:
            logging.error(f"Error in DQN replay: {e}")


class RLExecutionOptimizer:
    """
    Reinforcement Learning Order Execution Optimizer
    """
    
    def __init__(self, market_engine, trading_engine):
        self.logger = logging.getLogger(__name__)
        self.market_engine = market_engine
        self.trading_engine = trading_engine
        
        # RL Agent configuration
        self.state_size = 7  # Number of state features
        self.action_size = 10  # Discrete action space (0.1 to 1.0 in 0.1 increments)
        self.agent = DQNAgent(self.state_size, self.action_size) if RL_AVAILABLE else None
        
        # Execution parameters
        self.min_order_value = 10000  # $10k minimum for RL optimization
        self.max_slippage_threshold = 0.02  # 2% maximum slippage
        self.vwap_window = 20  # 20-period VWAP for benchmarking
        
        # Performance tracking
        self.execution_history = []
        self.training_episodes = 0
        self.total_slippage_saved = 0.0
        
        # Model persistence
        self.model_dir = "models"
        self.agent_path = os.path.join(self.model_dir, "rl_execution_agent.h5")
        
        # Initialize
        self._ensure_model_directory()
        self._load_agent()
        
        self.logger.info(f"🤖 RL Execution Optimizer initialized (RL: {'✅' if RL_AVAILABLE else '❌'})")
    
    def _ensure_model_directory(self):
        """Ensure model directory exists"""
        if not os.path.exists(self.model_dir):
            os.makedirs(self.model_dir)
    
    def _load_agent(self):
        """Load trained agent if available"""
        try:
            if RL_AVAILABLE and self.agent and os.path.exists(self.agent_path):
                self.agent.q_network.load_weights(self.agent_path)
                self.agent.update_target_network()
                self.logger.info("✅ Loaded trained RL execution agent")
        except Exception as e:
            self.logger.error(f"Error loading RL agent: {e}")
    
    def _save_agent(self):
        """Save trained agent"""
        try:
            if RL_AVAILABLE and self.agent and self.agent.q_network:
                self.agent.q_network.save_weights(self.agent_path)
                self.logger.info("💾 RL execution agent saved")
        except Exception as e:
            self.logger.error(f"Error saving RL agent: {e}")
    
    @performance_optimizer.performance_monitor("rl_optimize_execution")
    async def optimize_execution(self, symbol: str, total_quantity: float, 
                               side: str, max_time_minutes: int = 30) -> ExecutionResult:
        """Optimize order execution using RL agent"""
        try:
            order_value = total_quantity * await self._get_current_price(symbol)
            
            # Use RL optimization for large orders only
            if order_value < self.min_order_value:
                return await self._simple_execution(symbol, total_quantity, side)
            
            # Initialize execution state
            remaining_quantity = total_quantity
            executed_quantity = 0.0
            total_cost = 0.0
            start_time = datetime.now()
            max_time = timedelta(minutes=max_time_minutes)
            
            # Get initial VWAP benchmark
            vwap_benchmark = await self._calculate_vwap(symbol)
            
            execution_steps = []
            
            while remaining_quantity > 0 and (datetime.now() - start_time) < max_time:
                # Get current market state
                state = await self._get_execution_state(
                    symbol, total_quantity, executed_quantity, remaining_quantity
                )
                
                if not state:
                    break
                
                # Get action from RL agent
                action = self._get_execution_action(state)
                
                # Execute partial order
                step_result = await self._execute_partial_order(
                    symbol, remaining_quantity, action, side
                )
                
                if step_result and step_result.success:
                    executed_quantity += step_result.executed_quantity
                    remaining_quantity -= step_result.executed_quantity
                    total_cost += step_result.executed_quantity * step_result.average_price
                    
                    execution_steps.append(step_result)
                    
                    # Train agent if RL is available
                    if RL_AVAILABLE and self.agent:
                        reward = self._calculate_reward(step_result, vwap_benchmark)
                        next_state = await self._get_execution_state(
                            symbol, total_quantity, executed_quantity, remaining_quantity
                        )
                        
                        done = remaining_quantity <= 0
                        self.agent.remember(
                            state.to_array(), 
                            self._action_to_index(action),
                            reward,
                            next_state.to_array() if next_state else state.to_array(),
                            done
                        )
                        
                        # Train periodically
                        if len(self.agent.memory) > 32:
                            self.agent.replay()
                
                # Small delay between executions
                await asyncio.sleep(1)
            
            # Calculate final metrics
            average_price = total_cost / max(executed_quantity, 1)
            slippage = self._calculate_slippage(average_price, vwap_benchmark)
            market_impact = self._calculate_market_impact(execution_steps)
            
            result = ExecutionResult(
                symbol=symbol,
                executed_quantity=executed_quantity,
                average_price=average_price,
                slippage=slippage,
                market_impact=market_impact,
                vwap_benchmark=vwap_benchmark,
                execution_time=(datetime.now() - start_time).total_seconds(),
                success=executed_quantity > 0,
                timestamp=datetime.now()
            )
            
            # Track performance
            self.execution_history.append(result)
            
            # Save agent periodically
            if len(self.execution_history) % 10 == 0:
                self._save_agent()
            
            self.logger.info(f"🤖 RL execution completed: {executed_quantity} {symbol}, slippage: {slippage:.4f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in RL execution optimization: {e}")
            return await self._simple_execution(symbol, total_quantity, side)
    
    async def _get_execution_state(self, symbol: str, total_quantity: float, 
                                 executed_quantity: float, remaining_quantity: float) -> Optional[ExecutionState]:
        """Get current execution state for RL agent"""
        try:
            # Get market data
            quote = await self.market_engine.market_data.get_real_time_quote(symbol)
            if not quote:
                return None
            
            # Calculate state features
            fill_percentage = executed_quantity / max(total_quantity, 1)
            bid_ask_spread = (quote.ask - quote.bid) / quote.price
            
            # Get recent price impact (simplified)
            recent_price_impact = 0.001  # Default 0.1%
            
            # Volume rate (simplified)
            volume_rate = 1.0  # Default normalized volume
            
            # Time remaining (normalized)
            time_remaining = max(0, 1 - fill_percentage)
            
            # Market volatility (simplified)
            market_volatility = bid_ask_spread * 10  # Use spread as proxy
            
            return ExecutionState(
                order_size=total_quantity,
                fill_percentage=fill_percentage,
                recent_price_impact=recent_price_impact,
                bid_ask_spread=bid_ask_spread,
                volume_rate=volume_rate,
                time_remaining=time_remaining,
                market_volatility=market_volatility
            )
            
        except Exception as e:
            self.logger.error(f"Error getting execution state: {e}")
            return None
    
    def _get_execution_action(self, state: ExecutionState) -> ExecutionAction:
        """Get execution action from RL agent or fallback logic"""
        try:
            if RL_AVAILABLE and self.agent:
                action_index = self.agent.act(state.to_array())
                fraction = (action_index + 1) * 0.1  # Convert to 0.1-1.0 range
            else:
                # Fallback logic
                if state.time_remaining < 0.2:  # Less than 20% time remaining
                    fraction = 0.8  # Execute aggressively
                elif state.market_volatility > 0.01:  # High volatility
                    fraction = 0.3  # Execute conservatively
                else:
                    fraction = 0.5  # Moderate execution
            
            # Determine order type based on market conditions
            if state.bid_ask_spread > 0.005:  # Wide spread
                order_type = 'limit'
                urgency = 'low'
            elif state.time_remaining < 0.3:  # Time pressure
                order_type = 'market'
                urgency = 'high'
            else:
                order_type = 'twap'
                urgency = 'medium'
            
            return ExecutionAction(
                fraction_to_execute=min(1.0, max(0.1, fraction)),
                order_type=order_type,
                urgency=urgency
            )
            
        except Exception as e:
            self.logger.error(f"Error getting execution action: {e}")
            return ExecutionAction(0.5, 'market', 'medium')  # Default action

    async def _execute_partial_order(self, symbol: str, remaining_quantity: float,
                                   action: ExecutionAction, side: str) -> Optional[ExecutionResult]:
        """Execute partial order based on RL action"""
        try:
            # Calculate execution quantity
            execution_quantity = remaining_quantity * action.fraction_to_execute

            # Place order through trading engine
            order_result = await self.trading_engine.place_order(
                symbol=symbol,
                qty=int(execution_quantity),
                side=side,
                type=action.order_type,
                time_in_force='day',
                source='rl_execution_optimizer'
            )

            if order_result and order_result.get('success'):
                # Get execution details
                fill_price = order_result.get('fill_price', 0)
                filled_qty = order_result.get('filled_qty', 0)

                return ExecutionResult(
                    symbol=symbol,
                    executed_quantity=filled_qty,
                    average_price=fill_price,
                    slippage=0.0,  # Will be calculated later
                    market_impact=0.0,  # Will be calculated later
                    vwap_benchmark=0.0,  # Will be set later
                    execution_time=1.0,  # Simplified
                    success=True,
                    timestamp=datetime.now()
                )
            else:
                return None

        except Exception as e:
            self.logger.error(f"Error executing partial order: {e}")
            return None

    async def _simple_execution(self, symbol: str, quantity: float, side: str) -> ExecutionResult:
        """Simple execution fallback for small orders"""
        try:
            start_time = datetime.now()

            # Get VWAP benchmark
            vwap_benchmark = await self._calculate_vwap(symbol)

            # Execute as market order
            order_result = await self.trading_engine.place_order(
                symbol=symbol,
                qty=int(quantity),
                side=side,
                type='market',
                time_in_force='day',
                source='simple_execution'
            )

            if order_result and order_result.get('success'):
                fill_price = order_result.get('fill_price', 0)
                filled_qty = order_result.get('filled_qty', 0)

                slippage = self._calculate_slippage(fill_price, vwap_benchmark)

                return ExecutionResult(
                    symbol=symbol,
                    executed_quantity=filled_qty,
                    average_price=fill_price,
                    slippage=slippage,
                    market_impact=0.001,  # Assume 0.1% market impact
                    vwap_benchmark=vwap_benchmark,
                    execution_time=(datetime.now() - start_time).total_seconds(),
                    success=True,
                    timestamp=datetime.now()
                )
            else:
                return ExecutionResult(
                    symbol=symbol,
                    executed_quantity=0,
                    average_price=0,
                    slippage=0,
                    market_impact=0,
                    vwap_benchmark=vwap_benchmark,
                    execution_time=0,
                    success=False,
                    timestamp=datetime.now()
                )

        except Exception as e:
            self.logger.error(f"Error in simple execution: {e}")
            return ExecutionResult(symbol, 0, 0, 0, 0, 0, 0, False, datetime.now())

    async def _get_current_price(self, symbol: str) -> float:
        """Get current price for symbol"""
        try:
            quote = await self.market_engine.market_data.get_real_time_quote(symbol)
            return quote.price if quote else 100.0  # Default fallback
        except Exception as e:
            self.logger.error(f"Error getting current price: {e}")
            return 100.0

    async def _calculate_vwap(self, symbol: str) -> float:
        """Calculate VWAP benchmark"""
        try:
            # Get recent historical data
            historical_data = await self.market_engine.market_data.get_historical_data(
                symbol, limit=self.vwap_window
            )

            if not historical_data or len(historical_data) < 5:
                return await self._get_current_price(symbol)

            # Calculate VWAP
            total_volume = sum(bar.volume for bar in historical_data)
            if total_volume == 0:
                return historical_data[-1].close

            vwap = sum(bar.close * bar.volume for bar in historical_data) / total_volume
            return vwap

        except Exception as e:
            self.logger.error(f"Error calculating VWAP: {e}")
            return await self._get_current_price(symbol)

    def _calculate_slippage(self, execution_price: float, benchmark_price: float) -> float:
        """Calculate slippage vs benchmark"""
        if benchmark_price == 0:
            return 0.0
        return (execution_price - benchmark_price) / benchmark_price

    def _calculate_market_impact(self, execution_steps: List[ExecutionResult]) -> float:
        """Calculate market impact from execution steps"""
        if not execution_steps:
            return 0.0

        # Simplified market impact calculation
        total_quantity = sum(step.executed_quantity for step in execution_steps)
        if total_quantity == 0:
            return 0.0

        # Assume market impact increases with order size
        impact = min(0.01, total_quantity / 100000)  # Max 1% impact
        return impact

    def _calculate_reward(self, execution_result: ExecutionResult, vwap_benchmark: float) -> float:
        """Calculate reward for RL agent"""
        try:
            # Reward based on slippage reduction
            slippage = abs(execution_result.slippage)

            # Positive reward for low slippage, negative for high slippage
            if slippage < 0.001:  # Less than 0.1% slippage
                reward = 1.0
            elif slippage < 0.005:  # Less than 0.5% slippage
                reward = 0.5
            elif slippage < 0.01:  # Less than 1% slippage
                reward = 0.0
            else:  # High slippage
                reward = -1.0

            # Bonus for successful execution
            if execution_result.success:
                reward += 0.2

            # Penalty for excessive market impact
            if execution_result.market_impact > 0.005:  # More than 0.5%
                reward -= 0.5

            return reward

        except Exception as e:
            self.logger.error(f"Error calculating reward: {e}")
            return 0.0

    def _action_to_index(self, action: ExecutionAction) -> int:
        """Convert action to index for RL agent"""
        # Convert fraction (0.1-1.0) to index (0-9)
        return max(0, min(9, int(action.fraction_to_execute * 10) - 1))

    async def train_agent(self, episodes: int = 100):
        """Train RL agent using historical data simulation"""
        try:
            if not RL_AVAILABLE or not self.agent:
                self.logger.warning("RL not available for training")
                return

            self.logger.info(f"🏋️ Starting RL agent training for {episodes} episodes...")

            # Training symbols
            training_symbols = ['SPY', 'QQQ', 'AAPL', 'MSFT', 'GOOGL']

            for episode in range(episodes):
                symbol = random.choice(training_symbols)
                quantity = random.uniform(1000, 10000)  # Random order size
                side = random.choice(['buy', 'sell'])

                # Simulate execution (simplified for training)
                try:
                    result = await self.optimize_execution(symbol, quantity, side, max_time_minutes=10)

                    if result.success:
                        self.training_episodes += 1

                        # Calculate slippage savings
                        baseline_slippage = 0.005  # Assume 0.5% baseline
                        slippage_saved = max(0, baseline_slippage - abs(result.slippage))
                        self.total_slippage_saved += slippage_saved

                except Exception as e:
                    self.logger.error(f"Training episode {episode} failed: {e}")
                    continue

                # Update target network periodically
                if episode % 10 == 0:
                    self.agent.update_target_network()
                    self.logger.info(f"Training progress: {episode}/{episodes} episodes")

            # Save trained agent
            self._save_agent()

            self.logger.info(f"✅ RL agent training completed: {self.training_episodes} episodes")

        except Exception as e:
            self.logger.error(f"Error training RL agent: {e}")

    def get_execution_performance(self) -> Dict[str, Any]:
        """Get execution performance metrics"""
        try:
            if not self.execution_history:
                return {'error': 'No execution history available'}

            # Calculate performance metrics
            successful_executions = [r for r in self.execution_history if r.success]

            if not successful_executions:
                return {'error': 'No successful executions'}

            avg_slippage = np.mean([abs(r.slippage) for r in successful_executions])
            avg_market_impact = np.mean([r.market_impact for r in successful_executions])
            avg_execution_time = np.mean([r.execution_time for r in successful_executions])

            # Calculate slippage reduction vs baseline
            baseline_slippage = 0.005  # 0.5% baseline
            slippage_reduction = max(0, (baseline_slippage - avg_slippage) / baseline_slippage * 100)

            return {
                'total_executions': len(self.execution_history),
                'successful_executions': len(successful_executions),
                'success_rate': len(successful_executions) / len(self.execution_history) * 100,
                'avg_slippage': avg_slippage,
                'avg_market_impact': avg_market_impact,
                'avg_execution_time': avg_execution_time,
                'slippage_reduction_vs_baseline': slippage_reduction,
                'total_slippage_saved': self.total_slippage_saved,
                'training_episodes': self.training_episodes,
                'agent_epsilon': self.agent.epsilon if self.agent else 0
            }

        except Exception as e:
            self.logger.error(f"Error getting execution performance: {e}")
            return {'error': str(e)}

    async def get_optimizer_status(self) -> Dict[str, Any]:
        """Get RL execution optimizer status"""
        return {
            'rl_available': RL_AVAILABLE,
            'agent_loaded': self.agent is not None,
            'execution_history_size': len(self.execution_history),
            'training_episodes': self.training_episodes,
            'min_order_value': self.min_order_value,
            'max_slippage_threshold': self.max_slippage_threshold,
            'vwap_window': self.vwap_window,
            'state_size': self.state_size,
            'action_size': self.action_size,
            'performance_metrics': self.get_execution_performance()
        }
