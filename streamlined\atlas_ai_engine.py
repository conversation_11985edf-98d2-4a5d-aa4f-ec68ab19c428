"""
A.T.L.A.S AI Engine - Consolidated AI Intelligence System
Combines all AI services, chain-of-thought reasoning, multi-agent coordination, and validation
"""

import asyncio
import logging
import openai
import re
import json
import sqlite3
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, <PERSON><PERSON>
from enum import Enum
from dataclasses import dataclass, asdict
import numpy as np

from config import settings
from models import (
    ChatMessage, AIResponse, ChainOfThoughtAnalysis, Quote,
    TechnicalIndicators, NewsArticle, SentimentAnalysis
)


class CommunicationMode(Enum):
    """AI communication styles"""
    MENTOR = "mentor"
    PROFESSIONAL = "professional"
    CASUAL = "casual"
    AGGRESSIVE = "aggressive"


class AgentType(Enum):
    """Multi-agent system types"""
    TECHNICAL = "technical"
    RISK = "risk"
    SENTIMENT = "sentiment"
    EXECUTION = "execution"


@dataclass
class AgentAnalysis:
    """Analysis result from specialized agent"""
    agent_type: AgentType
    decision: float  # -1.0 to 1.0
    confidence: float
    reasoning: str
    data_points: Dict[str, Any]
    risk_factors: List[str]
    timestamp: datetime


@dataclass
class AgentConsensus:
    """Multi-agent consensus result"""
    final_decision: float
    consensus_confidence: float
    agent_votes: Dict[AgentType, AgentAnalysis]
    disagreement_score: float
    risk_assessment: str
    execution_recommendation: str


@dataclass
class TradingGoal:
    """User trading goal with context"""
    goal_type: str  # 'profit_target', 'risk_limit', 'learning', 'strategy_test'
    target_amount: Optional[float] = None
    timeframe: Optional[str] = None  # 'today', 'this_week', 'by_friday'
    risk_tolerance: str = 'moderate'  # 'conservative', 'moderate', 'aggressive'
    priority: int = 1  # 1=highest, 5=lowest
    created_at: datetime = None
    status: str = 'active'  # 'active', 'completed', 'cancelled'
    progress: float = 0.0  # 0.0 to 1.0


@dataclass
class ConversationContext:
    """Enhanced conversation context with memory"""
    session_id: str
    user_id: str
    current_goal: Optional[TradingGoal] = None
    conversation_state: str = 'greeting'  # 'greeting', 'analyzing', 'confirming', 'executing'
    last_trade_summary: Optional[str] = None
    user_tone: str = 'neutral'  # 'excited', 'anxious', 'confident', 'frustrated'
    complexity_level: str = 'beginner'  # 'beginner', 'intermediate', 'advanced'
    rejected_signals: List[str] = None
    custom_instructions: List[str] = None

    def __post_init__(self):
        if self.rejected_signals is None:
            self.rejected_signals = []
        if self.custom_instructions is None:
            self.custom_instructions = []


@dataclass
class EmotionalState:
    """User emotional state analysis"""
    primary_emotion: str  # 'confident', 'anxious', 'greedy', 'fearful', 'neutral'
    confidence_level: float  # 0.0 to 1.0
    risk_seeking: float  # -1.0 (risk averse) to 1.0 (risk seeking)
    urgency_level: float  # 0.0 (patient) to 1.0 (urgent)
    warning_flags: List[str] = None  # ['revenge_trading', 'overconfident', 'panic']

    def __post_init__(self):
        if self.warning_flags is None:
            self.warning_flags = []


class EnhancedContextMemory:
    """Enhanced context memory system with persistent storage"""

    def __init__(self, db_path: str = "atlas_enhanced_memory.db"):
        self.logger = logging.getLogger(__name__)
        self.db_path = db_path
        self._initialize_database()

    def _initialize_database(self):
        """Initialize enhanced memory database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # User profiles table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id TEXT PRIMARY KEY,
                    complexity_level TEXT DEFAULT 'beginner',
                    risk_tolerance TEXT DEFAULT 'moderate',
                    communication_style TEXT DEFAULT 'mentor',
                    custom_instructions TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')

            # Trading goals table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trading_goals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    goal_type TEXT,
                    target_amount REAL,
                    timeframe TEXT,
                    risk_tolerance TEXT,
                    priority INTEGER,
                    status TEXT DEFAULT 'active',
                    progress REAL DEFAULT 0.0,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')

            # Conversation context table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversation_context (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT,
                    current_goal_id INTEGER,
                    conversation_state TEXT DEFAULT 'greeting',
                    last_trade_summary TEXT,
                    user_tone TEXT DEFAULT 'neutral',
                    rejected_signals TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')

            # Rejected signals table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rejected_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    signal_type TEXT,
                    symbol TEXT,
                    reason TEXT,
                    rejected_at TEXT
                )
            ''')

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error initializing enhanced memory database: {e}")

    def store_trading_goal(self, user_id: str, goal: TradingGoal) -> int:
        """Store trading goal and return goal ID"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO trading_goals
                (user_id, goal_type, target_amount, timeframe, risk_tolerance, priority, status, progress, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_id, goal.goal_type, goal.target_amount, goal.timeframe,
                goal.risk_tolerance, goal.priority, goal.status, goal.progress,
                datetime.now().isoformat(), datetime.now().isoformat()
            ))

            goal_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return goal_id

        except Exception as e:
            self.logger.error(f"Error storing trading goal: {e}")
            return -1

    def get_active_goal(self, user_id: str) -> Optional[TradingGoal]:
        """Get user's active trading goal"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT goal_type, target_amount, timeframe, risk_tolerance, priority, status, progress, created_at
                FROM trading_goals
                WHERE user_id = ? AND status = 'active'
                ORDER BY priority ASC, created_at DESC
                LIMIT 1
            ''', (user_id,))

            row = cursor.fetchone()
            conn.close()

            if row:
                return TradingGoal(
                    goal_type=row[0],
                    target_amount=row[1],
                    timeframe=row[2],
                    risk_tolerance=row[3],
                    priority=row[4],
                    status=row[5],
                    progress=row[6],
                    created_at=datetime.fromisoformat(row[7])
                )

            return None

        except Exception as e:
            self.logger.error(f"Error getting active goal: {e}")
            return None

    def store_rejected_signal(self, user_id: str, signal_type: str, symbol: str, reason: str):
        """Store rejected trading signal"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO rejected_signals (user_id, signal_type, symbol, reason, rejected_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, signal_type, symbol, reason, datetime.now().isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error storing rejected signal: {e}")


class EmotionalIntelligence:
    """Emotional intelligence and tone analysis"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Emotional keywords mapping
        self.emotion_keywords = {
            'anxious': ['worried', 'nervous', 'scared', 'afraid', 'panic', 'stress'],
            'greedy': ['need to make', 'must win', 'big money', 'get rich', 'make it back'],
            'confident': ['sure', 'certain', 'know', 'confident', 'positive'],
            'frustrated': ['annoyed', 'angry', 'mad', 'upset', 'hate', 'stupid'],
            'fearful': ['lose', 'loss', 'afraid', 'risk', 'dangerous', 'safe']
        }

        # Warning phrases for revenge trading
        self.revenge_phrases = [
            'make it back', 'need to win', 'must recover', 'lost money',
            'get even', 'make up for', 'recoup losses'
        ]

        # Urgency indicators
        self.urgency_phrases = [
            'today', 'now', 'quickly', 'fast', 'urgent', 'asap',
            'before close', 'by end of day', 'this morning'
        ]

    def analyze_emotional_state(self, message: str) -> EmotionalState:
        """Analyze user's emotional state from message"""

        message_lower = message.lower()
        emotions_detected = {}
        warning_flags = []

        # Detect emotions
        for emotion, keywords in self.emotion_keywords.items():
            score = sum(1 for keyword in keywords if keyword in message_lower)
            if score > 0:
                emotions_detected[emotion] = score

        # Determine primary emotion
        if emotions_detected:
            primary_emotion = max(emotions_detected.keys(), key=lambda k: emotions_detected[k])
            confidence = min(emotions_detected[primary_emotion] / 3.0, 1.0)
        else:
            primary_emotion = 'neutral'
            confidence = 0.5

        # Check for revenge trading
        if any(phrase in message_lower for phrase in self.revenge_phrases):
            warning_flags.append('revenge_trading')

        # Check urgency
        urgency_level = 0.0
        if any(phrase in message_lower for phrase in self.urgency_phrases):
            urgency_level = 0.8

        # Calculate risk seeking based on emotion
        risk_seeking = 0.0
        if primary_emotion == 'greedy':
            risk_seeking = 0.7
        elif primary_emotion == 'confident':
            risk_seeking = 0.3
        elif primary_emotion == 'fearful':
            risk_seeking = -0.5
        elif primary_emotion == 'anxious':
            risk_seeking = -0.3

        return EmotionalState(
            primary_emotion=primary_emotion,
            confidence_level=confidence,
            risk_seeking=risk_seeking,
            urgency_level=urgency_level,
            warning_flags=warning_flags
        )


class GoalParser:
    """Advanced goal and intent parsing"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Goal type patterns
        self.goal_patterns = {
            'profit_target': [
                r'make \$?(\d+)', r'profit \$?(\d+)', r'earn \$?(\d+)',
                r'(\d+) dollars?', r'(\d+) bucks?'
            ],
            'risk_limit': [
                r'risk \$?(\d+)', r'lose \$?(\d+)', r'max loss \$?(\d+)'
            ],
            'learning': [
                r'learn', r'understand', r'explain', r'teach', r'how to'
            ],
            'strategy_test': [
                r'test', r'try', r'experiment', r'see if'
            ]
        }

        # Timeframe patterns
        self.timeframe_patterns = {
            'today': [r'today', r'by end of day', r'before close'],
            'this_week': [r'this week', r'by friday', r'weekly'],
            'this_month': [r'this month', r'monthly'],
            'long_term': [r'long term', r'eventually', r'over time']
        }

    def parse_trading_goal(self, message: str, user_id: str) -> Optional[TradingGoal]:
        """Parse trading goal from user message"""

        message_lower = message.lower()

        # Detect goal type and extract amount
        goal_type = 'learning'  # default
        target_amount = None

        for gtype, patterns in self.goal_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, message_lower)
                if match:
                    goal_type = gtype
                    if match.groups():
                        try:
                            target_amount = float(match.group(1))
                        except (ValueError, IndexError):
                            pass
                    break
            if goal_type != 'learning':
                break

        # Detect timeframe
        timeframe = None
        for tf, patterns in self.timeframe_patterns.items():
            if any(re.search(pattern, message_lower) for pattern in patterns):
                timeframe = tf
                break

        # Only create goal if we detected a clear intent
        if goal_type != 'learning' or timeframe or target_amount:
            return TradingGoal(
                goal_type=goal_type,
                target_amount=target_amount,
                timeframe=timeframe,
                created_at=datetime.now()
            )

        return None


class ChainOfThoughtEngine:
    """Core reasoning engine with step-by-step analysis"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def analyze_trade_setup(self, symbol: str, quote: Quote, 
                                 indicators: TechnicalIndicators,
                                 market_context: Dict[str, Any]) -> ChainOfThoughtAnalysis:
        """Perform comprehensive chain-of-thought analysis"""
        
        steps = []
        confidence_factors = []
        
        # Step 1: Market Context Analysis
        market_step = await self._analyze_market_context(market_context)
        steps.append(market_step)
        confidence_factors.append(market_step.get('confidence', 0.5))
        
        # Step 2: Technical Analysis
        technical_step = await self._analyze_technical_setup(symbol, quote, indicators)
        steps.append(technical_step)
        confidence_factors.append(technical_step.get('confidence', 0.5))
        
        # Step 3: Risk Assessment
        risk_step = await self._analyze_risk_factors(symbol, quote, indicators)
        steps.append(risk_step)
        confidence_factors.append(risk_step.get('confidence', 0.5))
        
        # Step 4: Entry/Exit Strategy
        strategy_step = await self._develop_entry_exit_strategy(symbol, quote, indicators)
        steps.append(strategy_step)
        confidence_factors.append(strategy_step.get('confidence', 0.5))
        
        # Calculate final confidence
        final_confidence = sum(confidence_factors) / len(confidence_factors)
        
        # Generate educational explanation
        explanation = self._generate_educational_explanation(steps, final_confidence)
        
        return ChainOfThoughtAnalysis(
            symbol=symbol,
            analysis_steps=steps,
            final_confidence=final_confidence,
            recommendation="BUY" if final_confidence > 0.7 else "HOLD" if final_confidence > 0.4 else "AVOID",
            reasoning=explanation,
            risk_factors=[step.get('risks', []) for step in steps],
            educational_notes=[step.get('education', '') for step in steps],
            timestamp=datetime.utcnow()
        )
    
    async def _analyze_market_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze overall market conditions"""
        return {
            "step": "Market Context Analysis",
            "analysis": "Evaluating overall market conditions and sentiment",
            "confidence": 0.6,
            "education": "Market context helps determine if it's a good time to trade"
        }
    
    async def _analyze_technical_setup(self, symbol: str, quote: Quote, 
                                     indicators: TechnicalIndicators) -> Dict[str, Any]:
        """Analyze technical indicators and patterns"""
        return {
            "step": "Technical Analysis",
            "analysis": f"Analyzing {symbol} technical indicators and price action",
            "confidence": 0.7,
            "education": "Technical analysis helps identify entry and exit points"
        }
    
    async def _analyze_risk_factors(self, symbol: str, quote: Quote,
                                  indicators: TechnicalIndicators) -> Dict[str, Any]:
        """Assess risk factors and position sizing"""
        return {
            "step": "Risk Assessment",
            "analysis": "Evaluating potential risks and appropriate position size",
            "confidence": 0.8,
            "risks": ["Market volatility", "Sector risk"],
            "education": "Risk management is the most important aspect of trading"
        }
    
    async def _develop_entry_exit_strategy(self, symbol: str, quote: Quote,
                                         indicators: TechnicalIndicators) -> Dict[str, Any]:
        """Develop entry and exit strategy"""
        return {
            "step": "Entry/Exit Strategy",
            "analysis": "Determining optimal entry price, stop loss, and profit targets",
            "confidence": 0.6,
            "education": "Having a clear plan before entering helps maintain discipline"
        }
    
    def _generate_educational_explanation(self, steps: List[Dict], confidence: float) -> str:
        """Generate beginner-friendly explanation"""
        explanation = "Here's my step-by-step analysis:\n\n"
        
        for i, step in enumerate(steps, 1):
            explanation += f"{i}. {step['step']}: {step['analysis']}\n"
            if 'education' in step:
                explanation += f"   💡 {step['education']}\n\n"
        
        explanation += f"Overall Confidence: {confidence*100:.0f}%\n"
        
        if confidence > 0.7:
            explanation += "This looks like a strong setup with good risk/reward potential."
        elif confidence > 0.4:
            explanation += "This is a moderate setup - proceed with caution and smaller position size."
        else:
            explanation += "This setup has too many risks - better to wait for a clearer opportunity."
        
        return explanation


class MultiAgentCoordinator:
    """Coordinates multiple specialized trading agents"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.agent_weights = {
            AgentType.TECHNICAL: 0.35,
            AgentType.RISK: 0.30,
            AgentType.SENTIMENT: 0.20,
            AgentType.EXECUTION: 0.15
        }
    
    async def get_consensus_analysis(self, symbol: str, quote: Quote,
                                   indicators: TechnicalIndicators,
                                   market_context: Dict[str, Any]) -> AgentConsensus:
        """Get consensus from all agents"""
        
        agent_analyses = {}
        
        # Technical Analysis Agent
        agent_analyses[AgentType.TECHNICAL] = await self._technical_agent_analysis(
            symbol, quote, indicators
        )
        
        # Risk Management Agent
        agent_analyses[AgentType.RISK] = await self._risk_agent_analysis(
            symbol, quote, indicators
        )
        
        # Sentiment Analysis Agent
        agent_analyses[AgentType.SENTIMENT] = await self._sentiment_agent_analysis(
            symbol, market_context
        )
        
        # Execution Timing Agent
        agent_analyses[AgentType.EXECUTION] = await self._execution_agent_analysis(
            symbol, quote, market_context
        )
        
        # Calculate weighted consensus
        weighted_decision = sum(
            analysis.decision * self.agent_weights[agent_type]
            for agent_type, analysis in agent_analyses.items()
        )
        
        # Calculate consensus confidence
        consensus_confidence = sum(
            analysis.confidence * self.agent_weights[agent_type]
            for agent_type, analysis in agent_analyses.items()
        )
        
        # Calculate disagreement score
        decisions = [analysis.decision for analysis in agent_analyses.values()]
        disagreement_score = max(decisions) - min(decisions)
        
        return AgentConsensus(
            final_decision=weighted_decision,
            consensus_confidence=consensus_confidence,
            agent_votes=agent_analyses,
            disagreement_score=disagreement_score,
            risk_assessment=self._generate_risk_assessment(agent_analyses),
            execution_recommendation=self._generate_execution_recommendation(
                weighted_decision, consensus_confidence, disagreement_score
            )
        )
    
    async def _technical_agent_analysis(self, symbol: str, quote: Quote,
                                      indicators: TechnicalIndicators) -> AgentAnalysis:
        """Technical analysis agent"""
        # Simplified technical analysis
        decision = 0.5  # Placeholder
        confidence = 0.7
        
        return AgentAnalysis(
            agent_type=AgentType.TECHNICAL,
            decision=decision,
            confidence=confidence,
            reasoning="Technical indicators show moderate bullish signals",
            data_points={"rsi": indicators.rsi, "macd": indicators.macd_signal},
            risk_factors=["Overbought conditions"],
            timestamp=datetime.utcnow()
        )
    
    async def _risk_agent_analysis(self, symbol: str, quote: Quote,
                                 indicators: TechnicalIndicators) -> AgentAnalysis:
        """Risk management agent"""
        decision = 0.3  # Conservative
        confidence = 0.8
        
        return AgentAnalysis(
            agent_type=AgentType.RISK,
            decision=decision,
            confidence=confidence,
            reasoning="Risk factors are manageable with proper position sizing",
            data_points={"volatility": indicators.atr, "volume": quote.volume},
            risk_factors=["Market volatility", "Position concentration"],
            timestamp=datetime.utcnow()
        )
    
    async def _sentiment_agent_analysis(self, symbol: str,
                                      market_context: Dict[str, Any]) -> AgentAnalysis:
        """Sentiment analysis agent"""
        decision = 0.4
        confidence = 0.6
        
        return AgentAnalysis(
            agent_type=AgentType.SENTIMENT,
            decision=decision,
            confidence=confidence,
            reasoning="Market sentiment is neutral to slightly positive",
            data_points={"news_sentiment": 0.1, "social_sentiment": 0.2},
            risk_factors=["Mixed sentiment signals"],
            timestamp=datetime.utcnow()
        )
    
    async def _execution_agent_analysis(self, symbol: str, quote: Quote,
                                      market_context: Dict[str, Any]) -> AgentAnalysis:
        """Execution timing agent"""
        decision = 0.6
        confidence = 0.7
        
        return AgentAnalysis(
            agent_type=AgentType.EXECUTION,
            decision=decision,
            confidence=confidence,
            reasoning="Good execution conditions with adequate liquidity",
            data_points={"spread": quote.bid_ask_spread, "volume": quote.volume},
            risk_factors=["Market hours", "Liquidity"],
            timestamp=datetime.utcnow()
        )
    
    def _generate_risk_assessment(self, analyses: Dict[AgentType, AgentAnalysis]) -> str:
        """Generate overall risk assessment"""
        risk_factors = []
        for analysis in analyses.values():
            risk_factors.extend(analysis.risk_factors)
        
        unique_risks = list(set(risk_factors))
        return f"Key risks: {', '.join(unique_risks[:3])}"
    
    def _generate_execution_recommendation(self, decision: float, confidence: float,
                                         disagreement: float) -> str:
        """Generate execution recommendation"""
        if confidence > 0.7 and disagreement < 0.3:
            return "EXECUTE - Strong consensus with high confidence"
        elif confidence > 0.5 and disagreement < 0.5:
            return "PROCEED WITH CAUTION - Moderate consensus"
        else:
            return "WAIT - Low consensus or high disagreement among agents"


class AIValidationSystem:
    """AI response validation and grounding system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def validate_trading_response(self, response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Validate AI trading response for accuracy and safety"""
        
        validation_result = {
            "is_valid": True,
            "confidence": 0.8,
            "warnings": [],
            "corrections": [],
            "safety_flags": []
        }
        
        # Check for unrealistic claims
        if self._contains_unrealistic_claims(response):
            validation_result["warnings"].append("Response contains potentially unrealistic claims")
            validation_result["confidence"] *= 0.8
        
        # Check for proper risk disclaimers
        if not self._has_risk_disclaimers(response):
            validation_result["warnings"].append("Missing appropriate risk disclaimers")
            validation_result["corrections"].append("Add risk management reminders")
        
        # Check for educational content
        if not self._has_educational_content(response):
            validation_result["warnings"].append("Could benefit from more educational content")
        
        return validation_result
    
    def _contains_unrealistic_claims(self, response: str) -> bool:
        """Check for unrealistic trading claims"""
        unrealistic_patterns = [
            r"guaranteed.*profit",
            r"100%.*success",
            r"risk.*free",
            r"always.*profitable"
        ]
        
        for pattern in unrealistic_patterns:
            if re.search(pattern, response.lower()):
                return True
        return False
    
    def _has_risk_disclaimers(self, response: str) -> bool:
        """Check if response includes appropriate risk warnings"""
        risk_keywords = ["risk", "loss", "careful", "caution", "paper trading"]
        return any(keyword in response.lower() for keyword in risk_keywords)
    
    def _has_educational_content(self, response: str) -> bool:
        """Check if response includes educational elements"""
        educational_keywords = ["because", "reason", "why", "how", "learn", "understand"]
        return any(keyword in response.lower() for keyword in educational_keywords)


class ConversationalFlowEngine:
    """Enhanced conversational flow with clarification and dialog trees"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def needs_clarification(self, message: str, context: Dict[str, Any]) -> Optional[str]:
        """Determine if message needs clarification"""

        message_lower = message.lower()

        # Check for ambiguous requests
        ambiguous_patterns = [
            (r'\bmake money\b', "How much would you like to make and by when?"),
            (r'\bbest stock\b', "Best for what timeframe? Day trading, swing trading, or long-term?"),
            (r'\bsafe\b.*\btrade\b', "What's your definition of 'safe'? Low volatility or small position size?"),
            (r'\bquick\b.*\bprofit\b', "How quick? Today, this week, or within a month?"),
            (r'\bgood\b.*\bentry\b', "Good entry for which strategy? Momentum, mean reversion, or breakout?")
        ]

        for pattern, clarification in ambiguous_patterns:
            if re.search(pattern, message_lower):
                return clarification

        # Check if symbol mentioned but no clear action
        if self._has_symbol_but_no_action(message):
            return "What would you like me to analyze about this stock? Technical setup, risk assessment, or entry/exit points?"

        return None

    def _has_symbol_but_no_action(self, message: str) -> bool:
        """Check if message has symbol but no clear action"""
        # Simple check for stock symbols without action words
        has_symbol = bool(re.search(r'\b[A-Z]{1,5}\b', message))
        action_words = ['analyze', 'buy', 'sell', 'trade', 'look at', 'check', 'what about']
        has_action = any(word in message.lower() for word in action_words)

        return has_symbol and not has_action

    def generate_follow_up_suggestions(self, context: Dict[str, Any]) -> List[str]:
        """Generate follow-up suggestions based on context"""

        suggestions = []

        # Based on conversation state
        state = context.get('conversation_state', 'greeting')

        if state == 'analyzing':
            suggestions.extend([
                "Would you like me to scan for more opportunities?",
                "Should I explain this setup in more detail?",
                "Want to see the risk assessment for this trade?"
            ])

        elif state == 'confirming':
            suggestions.extend([
                "Shall I proceed with this trade recommendation?",
                "Would you prefer a smaller position size?",
                "Want to see alternative setups?"
            ])

        # Based on user goal
        goal = context.get('current_goal')
        if goal and goal.get('goal_type') == 'profit_target':
            suggestions.append(f"Want me to find trades to help reach your ${goal.get('target_amount', 0)} goal?")

        return suggestions[:3]  # Limit to 3 suggestions

    def should_confirm_trade(self, confidence: float, risk_level: str, emotional_state: EmotionalState) -> bool:
        """Determine if trade needs confirmation"""

        # Always confirm if confidence is low
        if confidence < 0.7:
            return True

        # Confirm if user shows warning signs
        if emotional_state.warning_flags:
            return True

        # Confirm if high risk
        if risk_level in ['high', 'extreme']:
            return True

        # Confirm if user is urgent/emotional
        if emotional_state.urgency_level > 0.6:
            return True

        return False


class GroundingEngine:
    """Fact-checking and grounding engine to prevent hallucinations"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Trading book quotes for grounding
        self.book_quotes = {
            'risk_management': {
                'source': 'Trading in the Zone - Mark Douglas',
                'quote': 'The best traders predefine their risk before entering any trade.'
            },
            'psychology': {
                'source': 'Trading in the Zone - Mark Douglas',
                'quote': 'The biggest challenge traders face is not market analysis, but managing their own psychology.'
            },
            'cutting_losses': {
                'source': 'Market Wizards - Ed Seykota',
                'quote': 'The elements of good trading are cutting losses, cutting losses, and cutting losses.'
            },
            'position_sizing': {
                'source': 'Market Wizards - Michael Marcus',
                'quote': 'I never risk more than 1-2% of my total equity on any trade.'
            }
        }

    def ground_response(self, response: str, topic: str) -> str:
        """Add grounding sources to response"""

        # Find relevant book quote
        relevant_quote = None
        for key, quote_data in self.book_quotes.items():
            if key in topic.lower():
                relevant_quote = quote_data
                break

        if relevant_quote:
            response += f"\n\n📚 **{relevant_quote['source']}**: \"{relevant_quote['quote']}\""

        return response

    def validate_trading_facts(self, claims: List[str]) -> Dict[str, bool]:
        """Validate trading-related factual claims"""

        validated = {}

        for claim in claims:
            claim_lower = claim.lower()

            # Basic fact checking
            if 'guaranteed' in claim_lower and 'profit' in claim_lower:
                validated[claim] = False  # No guaranteed profits
            elif 'risk free' in claim_lower:
                validated[claim] = False  # No risk-free trading
            elif '100%' in claim_lower and ('win rate' in claim_lower or 'success' in claim_lower):
                validated[claim] = False  # No 100% win rates
            else:
                validated[claim] = True  # Assume valid if no red flags

        return validated


class AtlasAIEngine:
    """Main AI Engine coordinating all AI functionality with enhanced conversational intelligence"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Core engines
        self.cot_engine = ChainOfThoughtEngine()
        self.multi_agent = MultiAgentCoordinator()
        self.validation = AIValidationSystem()

        # Enhanced conversational intelligence
        self.context_memory = EnhancedContextMemory()
        self.emotional_intelligence = EmotionalIntelligence()
        self.goal_parser = GoalParser()
        self.conversation_flow = ConversationalFlowEngine()
        self.grounding_engine = GroundingEngine()

        # Configuration
        self.communication_mode = CommunicationMode.MENTOR

        # Initialize OpenAI
        openai.api_key = settings.OPENAI_API_KEY
    
    async def process_trading_query(self, message: str, context: Dict[str, Any]) -> AIResponse:
        """Process trading query with enhanced conversational intelligence"""

        try:
            # Get user context
            session_id = context.get('session_id', 'default')
            user_id = context.get('user_id', 'default_user')

            # Analyze emotional state
            emotional_state = self.emotional_intelligence.analyze_emotional_state(message)

            # Parse potential trading goal
            trading_goal = self.goal_parser.parse_trading_goal(message, user_id)
            if trading_goal:
                goal_id = self.context_memory.store_trading_goal(user_id, trading_goal)
                self.logger.info(f"New trading goal detected: {trading_goal.goal_type}")

            # Get active goal
            active_goal = self.context_memory.get_active_goal(user_id)

            # Check if clarification is needed
            clarification = self.conversation_flow.needs_clarification(message, context)
            if clarification:
                return AIResponse(
                    response=clarification,
                    type="clarification_request",
                    confidence=0.9,
                    requires_action=False,
                    timestamp=datetime.now()
                )

            # Extract intent and symbols
            intent = self._classify_intent(message)
            symbol = self._extract_symbol(message)

            # Enhanced context with emotional and goal awareness
            enhanced_context = {
                **context,
                'emotional_state': emotional_state,
                'active_goal': active_goal,
                'user_id': user_id,
                'session_id': session_id
            }

            # Process based on intent and context
            if symbol and 'quote' in context:
                # Perform chain-of-thought analysis
                cot_analysis = await self.cot_engine.analyze_trade_setup(
                    symbol, context['quote'], context.get('indicators'), enhanced_context
                )

                # Get multi-agent consensus
                consensus = await self.multi_agent.get_consensus_analysis(
                    symbol, context['quote'], context.get('indicators'), enhanced_context
                )

                # Check if confirmation is needed
                needs_confirmation = self.conversation_flow.should_confirm_trade(
                    consensus.consensus_confidence,
                    'moderate',  # Would get from risk assessment
                    emotional_state
                )

                # Generate AI response with emotional awareness
                response_text = await self._generate_enhanced_response(
                    message, intent, cot_analysis, consensus, enhanced_context, needs_confirmation
                )
            else:
                # General trading question with emotional awareness
                response_text = await self._generate_enhanced_general_response(
                    message, intent, enhanced_context
                )

            # Apply grounding to prevent hallucinations
            response_text = self.grounding_engine.ground_response(response_text, intent)

            # Add follow-up suggestions
            follow_ups = self.conversation_flow.generate_follow_up_suggestions(enhanced_context)
            if follow_ups:
                response_text += f"\n\n💡 **What's next?**\n" + "\n".join(f"• {suggestion}" for suggestion in follow_ups)

            # Validate response
            validation = await self.validation.validate_trading_response(response_text, enhanced_context)

            # Apply any corrections
            if validation['corrections']:
                response_text = await self._apply_corrections(response_text, validation['corrections'])

            # Add emotional coaching if needed
            if emotional_state.warning_flags:
                response_text = self._add_emotional_coaching(response_text, emotional_state)

            return AIResponse(
                response=response_text,
                type=intent,
                confidence=validation['confidence'],
                requires_action=intent in ['trade_request', 'analysis_request'],
                emotional_state=asdict(emotional_state),
                active_goal=asdict(active_goal) if active_goal else None,
                timestamp=datetime.now()
            )

        except Exception as e:
            self.logger.error(f"Error processing trading query: {e}")
            return AIResponse(
                response="I apologize, but I encountered an error processing your request. Please try again.",
                type="error",
                confidence=0.0,
                timestamp=datetime.now()
            )
    
    def _classify_intent(self, message: str) -> str:
        """Classify user intent from message"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ['buy', 'sell', 'trade', 'position']):
            return 'trade_request'
        elif any(word in message_lower for word in ['analyze', 'analysis', 'chart', 'technical']):
            return 'analysis_request'
        elif any(word in message_lower for word in ['learn', 'explain', 'what is', 'how to']):
            return 'education_request'
        elif any(word in message_lower for word in ['portfolio', 'performance', 'profit']):
            return 'portfolio_request'
        else:
            return 'general_chat'
    
    def _extract_symbol(self, message: str) -> Optional[str]:
        """Extract stock symbol from message"""
        # Simple regex for stock symbols
        import re
        pattern = r'\b[A-Z]{1,5}\b'
        matches = re.findall(pattern, message.upper())
        
        # Filter out common words that aren't symbols
        common_words = {'THE', 'AND', 'OR', 'BUT', 'FOR', 'AT', 'TO', 'FROM', 'UP', 'DOWN', 'IN', 'OUT', 'ON', 'OFF', 'OVER', 'UNDER', 'AGAIN', 'FURTHER', 'THEN', 'ONCE'}
        symbols = [match for match in matches if match not in common_words and len(match) <= 5]
        
        return symbols[0] if symbols else None
    
    async def _generate_contextual_response(self, message: str, intent: str,
                                          cot_analysis: ChainOfThoughtAnalysis,
                                          consensus: AgentConsensus) -> str:
        """Generate contextual response with trading analysis"""
        
        prompt = f"""
        As A.T.L.A.S, an AI trading mentor, respond to this user message: "{message}"
        
        Chain-of-Thought Analysis:
        - Symbol: {cot_analysis.symbol}
        - Confidence: {cot_analysis.final_confidence*100:.0f}%
        - Recommendation: {cot_analysis.recommendation}
        - Reasoning: {cot_analysis.reasoning}
        
        Multi-Agent Consensus:
        - Final Decision: {consensus.final_decision:.2f}
        - Consensus Confidence: {consensus.consensus_confidence*100:.0f}%
        - Risk Assessment: {consensus.risk_assessment}
        - Execution Recommendation: {consensus.execution_recommendation}
        
        Respond in a {self.communication_mode.value} style with:
        1. Clear explanation of the analysis
        2. Educational insights for beginners
        3. Appropriate risk warnings
        4. Actionable recommendations
        
        Remember: This is paper trading for educational purposes.
        """
        
        try:
            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=500,
                temperature=0.7
            )
            return response.choices[0].message.content
        except Exception as e:
            self.logger.error(f"OpenAI API error: {e}")
            return f"Based on my analysis of {cot_analysis.symbol}, I see {cot_analysis.recommendation.lower()} signals with {cot_analysis.final_confidence*100:.0f}% confidence. {cot_analysis.reasoning}"
    
    async def _generate_general_response(self, message: str, intent: str, context: Dict[str, Any]) -> str:
        """Generate general trading response"""
        
        prompt = f"""
        As A.T.L.A.S, an AI trading mentor, respond to: "{message}"
        
        Intent: {intent}
        Communication Style: {self.communication_mode.value}
        
        Provide helpful, educational trading guidance with appropriate risk warnings.
        Remember: This is for paper trading and educational purposes only.
        """
        
        try:
            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=400,
                temperature=0.7
            )
            return response.choices[0].message.content
        except Exception as e:
            self.logger.error(f"OpenAI API error: {e}")
            return "I'm here to help you learn about trading! What specific topic would you like to explore?"
    
    async def _generate_enhanced_response(self, message: str, intent: str,
                                        cot_analysis: ChainOfThoughtAnalysis,
                                        consensus: AgentConsensus,
                                        context: Dict[str, Any],
                                        needs_confirmation: bool) -> str:
        """Generate enhanced response with emotional and goal awareness"""

        emotional_state = context.get('emotional_state')
        active_goal = context.get('active_goal')

        # Base response
        response = f"📊 **Analysis for {cot_analysis.symbol}:**\n\n"
        response += f"**Recommendation:** {cot_analysis.recommendation}\n"
        response += f"**Confidence:** {cot_analysis.final_confidence*100:.0f}%\n"
        response += f"**Reasoning:** {cot_analysis.reasoning}\n\n"

        # Add multi-agent consensus
        response += f"🤖 **AI Consensus:** {consensus.execution_recommendation}\n"
        response += f"**Risk Assessment:** {consensus.risk_assessment}\n\n"

        # Emotional coaching if needed
        if emotional_state and emotional_state.warning_flags:
            if 'revenge_trading' in emotional_state.warning_flags:
                response += "⚠️ **Emotional Check:** I notice you might be trying to recover losses. Remember, revenge trading often leads to bigger losses. Let's focus on high-probability setups instead.\n\n"

        # Goal progress if applicable
        if active_goal and active_goal.goal_type == 'profit_target':
            response += f"🎯 **Goal Progress:** Working toward your ${active_goal.target_amount} target"
            if active_goal.timeframe:
                response += f" {active_goal.timeframe}"
            response += f" (Progress: {active_goal.progress*100:.0f}%)\n\n"

        # Confirmation if needed
        if needs_confirmation:
            response += "🤔 **Confirmation Required:** This trade has some risk factors. Are you sure you want to proceed? I can also show you safer alternatives.\n\n"

        # Complexity adjustment
        complexity = context.get('complexity_level', 'beginner')
        if complexity == 'beginner':
            response += "💡 **For Beginners:** This analysis combines technical indicators with risk management. The confidence score tells you how strong the signal is."

        return response

    async def _generate_enhanced_general_response(self, message: str, intent: str,
                                                context: Dict[str, Any]) -> str:
        """Generate enhanced general response with emotional awareness"""

        emotional_state = context.get('emotional_state')
        active_goal = context.get('active_goal')

        # Handle emotional states
        if emotional_state:
            if emotional_state.primary_emotion == 'anxious':
                return "I can sense you might be feeling anxious about trading. That's completely normal! Let's start with some low-risk educational analysis. What would you like to learn about?"

            elif emotional_state.primary_emotion == 'greedy':
                return "I understand the excitement about making money, but let's focus on consistent, disciplined trading. What's a realistic profit target you'd like to work toward?"

            elif 'revenge_trading' in emotional_state.warning_flags:
                return "I notice you might be trying to recover from losses. The best traders know that revenge trading is dangerous. Let's take a step back and find a high-probability setup instead. What's your account size so I can suggest appropriate position sizes?"

        # Handle based on intent
        if intent == 'education_request':
            return "I'm here to help you learn! What trading concept would you like me to explain? I can cover technical analysis, risk management, trading psychology, or specific strategies."

        elif intent == 'general_chat':
            base_response = "I'm A.T.L.A.S, your AI trading mentor! I'm here to help you learn trading through safe paper trading practice."

            if active_goal:
                base_response += f" I see you're working toward a {active_goal.goal_type}"
                if active_goal.target_amount:
                    base_response += f" of ${active_goal.target_amount}"
                base_response += ". How can I help you with that today?"
            else:
                base_response += " What would you like to explore today? I can analyze stocks, explain trading concepts, or help you set trading goals."

            return base_response

        return "I'm here to help with your trading journey! What would you like to explore?"

    def _add_emotional_coaching(self, response: str, emotional_state: EmotionalState) -> str:
        """Add emotional coaching based on detected state"""

        coaching = "\n\n🧠 **Trading Psychology Note:** "

        if 'revenge_trading' in emotional_state.warning_flags:
            coaching += "Remember, the market will always be there tomorrow. Don't let emotions drive your decisions."

        elif emotional_state.urgency_level > 0.7:
            coaching += "I notice urgency in your message. The best trades often require patience. Rushing leads to mistakes."

        elif emotional_state.primary_emotion == 'greedy':
            coaching += "Greed can be dangerous in trading. Focus on consistent small wins rather than trying to hit home runs."

        elif emotional_state.primary_emotion == 'fearful':
            coaching += "Fear is natural in trading. Start with smaller positions to build confidence and experience."

        else:
            return response  # No coaching needed

        return response + coaching

    async def _apply_corrections(self, response: str, corrections: List[str]) -> str:
        """Apply validation corrections to response"""
        if "Add risk management reminders" in corrections:
            response += "\n\n⚠️ Remember: All trading involves risk. This is for educational purposes using paper trading only."

        return response

    def set_communication_mode(self, mode: CommunicationMode):
        """Set AI communication style"""
        self.communication_mode = mode
        self.logger.info(f"Communication mode set to: {mode.value}")
