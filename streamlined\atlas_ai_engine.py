"""
A.T.L.A.S AI Engine - Consolidated AI Intelligence System
Combines all AI services, chain-of-thought reasoning, multi-agent coordination, and validation
"""

import asyncio
import logging
import openai
import re
import json
import sqlite3
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, <PERSON><PERSON>
from enum import Enum
from dataclasses import dataclass, asdict
import numpy as np

from config import settings
from models import (
    ChatMessage, AIResponse, ChainOfThoughtAnalysis, Quote,
    TechnicalIndicators, NewsArticle, SentimentAnalysis
)
from atlas_performance_optimizer import performance_optimizer
from atlas_database_manager import db_manager


class CommunicationMode(Enum):
    """AI communication styles"""
    MENTOR = "mentor"
    PROFESSIONAL = "professional"
    CASUAL = "casual"
    AGGRESSIVE = "aggressive"


class AgentType(Enum):
    """Multi-agent system types"""
    TECHNICAL = "technical"
    RISK = "risk"
    SENTIMENT = "sentiment"
    EXECUTION = "execution"


@dataclass
class AgentAnalysis:
    """Analysis result from specialized agent"""
    agent_type: AgentType
    decision: float  # -1.0 to 1.0
    confidence: float
    reasoning: str
    data_points: Dict[str, Any]
    risk_factors: List[str]
    timestamp: datetime


@dataclass
class AgentConsensus:
    """Multi-agent consensus result"""
    final_decision: float
    consensus_confidence: float
    agent_votes: Dict[AgentType, AgentAnalysis]
    disagreement_score: float
    risk_assessment: str
    execution_recommendation: str


@dataclass
class TradingGoal:
    """User trading goal with context"""
    goal_type: str  # 'profit_target', 'risk_limit', 'learning', 'strategy_test'
    target_amount: Optional[float] = None
    timeframe: Optional[str] = None  # 'today', 'this_week', 'by_friday'
    risk_tolerance: str = 'moderate'  # 'conservative', 'moderate', 'aggressive'
    priority: int = 1  # 1=highest, 5=lowest
    created_at: datetime = None
    status: str = 'active'  # 'active', 'completed', 'cancelled'
    progress: float = 0.0  # 0.0 to 1.0


@dataclass
class ConversationContext:
    """Enhanced conversation context with memory"""
    session_id: str
    user_id: str
    current_goal: Optional[TradingGoal] = None
    conversation_state: str = 'greeting'  # 'greeting', 'analyzing', 'confirming', 'executing'
    last_trade_summary: Optional[str] = None
    user_tone: str = 'neutral'  # 'excited', 'anxious', 'confident', 'frustrated'
    complexity_level: str = 'beginner'  # 'beginner', 'intermediate', 'advanced'
    rejected_signals: List[str] = None
    custom_instructions: List[str] = None

    def __post_init__(self):
        if self.rejected_signals is None:
            self.rejected_signals = []
        if self.custom_instructions is None:
            self.custom_instructions = []


@dataclass
class EmotionalState:
    """User emotional state analysis"""
    primary_emotion: str  # 'confident', 'anxious', 'greedy', 'fearful', 'neutral'
    confidence_level: float  # 0.0 to 1.0
    risk_seeking: float  # -1.0 (risk averse) to 1.0 (risk seeking)
    urgency_level: float  # 0.0 (patient) to 1.0 (urgent)
    warning_flags: List[str] = None  # ['revenge_trading', 'overconfident', 'panic']

    def __post_init__(self):
        if self.warning_flags is None:
            self.warning_flags = []


class EnhancedContextMemory:
    """Enhanced context memory system with persistent storage"""

    def __init__(self, db_path: str = "atlas_enhanced_memory.db"):
        self.logger = logging.getLogger(__name__)
        self.db_path = db_path
        self._initialize_database()

    def _initialize_database(self):
        """Initialize enhanced memory database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # User profiles table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id TEXT PRIMARY KEY,
                    complexity_level TEXT DEFAULT 'beginner',
                    risk_tolerance TEXT DEFAULT 'moderate',
                    communication_style TEXT DEFAULT 'mentor',
                    custom_instructions TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')

            # Trading goals table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trading_goals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    goal_type TEXT,
                    target_amount REAL,
                    timeframe TEXT,
                    risk_tolerance TEXT,
                    priority INTEGER,
                    status TEXT DEFAULT 'active',
                    progress REAL DEFAULT 0.0,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')

            # Conversation context table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversation_context (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT,
                    current_goal_id INTEGER,
                    conversation_state TEXT DEFAULT 'greeting',
                    last_trade_summary TEXT,
                    user_tone TEXT DEFAULT 'neutral',
                    rejected_signals TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')

            # Rejected signals table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rejected_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    signal_type TEXT,
                    symbol TEXT,
                    reason TEXT,
                    rejected_at TEXT
                )
            ''')

            # User preferences table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_preferences (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    preference_type TEXT NOT NULL,
                    preference_value TEXT NOT NULL,
                    metadata TEXT,
                    updated_at TEXT NOT NULL,
                    UNIQUE(user_id, preference_type)
                )
            ''')

            # Behavioral patterns table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS behavioral_patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    pattern_type TEXT NOT NULL,
                    severity INTEGER NOT NULL,
                    context TEXT,
                    timestamp TEXT NOT NULL
                )
            ''')

            # Session continuity table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS session_continuity (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    session_id TEXT NOT NULL,
                    conversation_state TEXT,
                    last_topic TEXT,
                    context_data TEXT,
                    updated_at TEXT NOT NULL
                )
            ''')

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error initializing enhanced memory database: {e}")

    def store_trading_goal(self, user_id: str, goal: TradingGoal) -> int:
        """Store trading goal and return goal ID"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO trading_goals
                (user_id, goal_type, target_amount, timeframe, risk_tolerance, priority, status, progress, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_id, goal.goal_type, goal.target_amount, goal.timeframe,
                goal.risk_tolerance, goal.priority, goal.status, goal.progress,
                datetime.now().isoformat(), datetime.now().isoformat()
            ))

            goal_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return goal_id

        except Exception as e:
            self.logger.error(f"Error storing trading goal: {e}")
            return -1

    def get_active_goal(self, user_id: str) -> Optional[TradingGoal]:
        """Get user's active trading goal"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT goal_type, target_amount, timeframe, risk_tolerance, priority, status, progress, created_at
                FROM trading_goals
                WHERE user_id = ? AND status = 'active'
                ORDER BY priority ASC, created_at DESC
                LIMIT 1
            ''', (user_id,))

            row = cursor.fetchone()
            conn.close()

            if row:
                return TradingGoal(
                    goal_type=row[0],
                    target_amount=row[1],
                    timeframe=row[2],
                    risk_tolerance=row[3],
                    priority=row[4],
                    status=row[5],
                    progress=row[6],
                    created_at=datetime.fromisoformat(row[7])
                )

            return None

        except Exception as e:
            self.logger.error(f"Error getting active goal: {e}")
            return None

    def store_rejected_signal(self, user_id: str, signal_type: str, symbol: str, reason: str):
        """Store rejected trading signal"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO rejected_signals (user_id, signal_type, symbol, reason, rejected_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, signal_type, symbol, reason, datetime.now().isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error storing rejected signal: {e}")

    def store_user_preference(self, user_id: str, preference_type: str,
                            preference_value: str, metadata: Dict[str, Any] = None):
        """Store persistent user preference"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO user_preferences
                (user_id, preference_type, preference_value, metadata, updated_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, preference_type, preference_value,
                  json.dumps(metadata or {}), datetime.utcnow().isoformat()))

            conn.commit()
            conn.close()
            self.logger.info(f"Stored preference {preference_type} for user {user_id}")

        except Exception as e:
            self.logger.error(f"Error storing user preference: {e}")

    def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get all user preferences"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT preference_type, preference_value, metadata
                FROM user_preferences
                WHERE user_id = ?
            ''', (user_id,))

            preferences = {}
            for row in cursor.fetchall():
                preferences[row[0]] = {
                    'value': row[1],
                    'metadata': json.loads(row[2]) if row[2] else {}
                }

            conn.close()
            return preferences

        except Exception as e:
            self.logger.error(f"Error getting user preferences: {e}")
            return {}

    def store_behavioral_pattern(self, user_id: str, pattern_type: str,
                               severity: int, context: str = None):
        """Store behavioral pattern occurrence"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO behavioral_patterns
                (user_id, pattern_type, severity, context, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, pattern_type, severity, context, datetime.utcnow().isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error storing behavioral pattern: {e}")

    def get_behavioral_history(self, user_id: str, days: int = 30) -> List[Dict[str, Any]]:
        """Get behavioral pattern history"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cutoff_date = (datetime.utcnow() - timedelta(days=days)).isoformat()

            cursor.execute('''
                SELECT pattern_type, severity, context, timestamp
                FROM behavioral_patterns
                WHERE user_id = ? AND timestamp > ?
                ORDER BY timestamp DESC
            ''', (user_id, cutoff_date))

            patterns = []
            for row in cursor.fetchall():
                patterns.append({
                    'pattern_type': row[0],
                    'severity': row[1],
                    'context': row[2],
                    'timestamp': row[3]
                })

            conn.close()
            return patterns

        except Exception as e:
            self.logger.error(f"Error getting behavioral history: {e}")
            return []

    def store_session_continuity(self, user_id: str, session_id: str,
                               conversation_state: str, last_topic: str,
                               context_data: Dict[str, Any] = None):
        """Store session continuity data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO session_continuity
                (user_id, session_id, conversation_state, last_topic, context_data, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, session_id, conversation_state, last_topic,
                  json.dumps(context_data or {}), datetime.utcnow().isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error storing session continuity: {e}")

    def get_session_continuity(self, user_id: str, session_id: str) -> Dict[str, Any]:
        """Get session continuity data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT conversation_state, last_topic, context_data, updated_at
                FROM session_continuity
                WHERE user_id = ? AND session_id = ?
                ORDER BY updated_at DESC LIMIT 1
            ''', (user_id, session_id))

            row = cursor.fetchone()
            conn.close()

            if row:
                return {
                    'conversation_state': row[0],
                    'last_topic': row[1],
                    'context_data': json.loads(row[2]) if row[2] else {},
                    'updated_at': row[3]
                }

            return {}

        except Exception as e:
            self.logger.error(f"Error getting session continuity: {e}")
            return {}


class EmotionalIntelligence:
    """Emotional intelligence and tone analysis"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Enhanced emotional keywords mapping
        self.emotion_keywords = {
            'anxious': ['worried', 'nervous', 'scared', 'afraid', 'panic', 'stress', 'uncertain', 'doubt'],
            'greedy': ['need to make', 'must win', 'big money', 'get rich', 'make it back', 'all in', 'maximum'],
            'confident': ['sure', 'certain', 'know', 'confident', 'positive', 'easy money', 'guaranteed'],
            'frustrated': ['annoyed', 'angry', 'mad', 'upset', 'hate', 'stupid', 'rigged', 'unfair'],
            'fearful': ['lose', 'loss', 'afraid', 'risk', 'dangerous', 'safe', 'protect', 'careful'],
            'impatient': ['waiting', 'slow', 'boring', 'when', 'hurry', 'taking forever'],
            'overconfident': ['easy', 'simple', 'always works', 'never fails', 'guaranteed win']
        }

        # Enhanced warning phrases for harmful behaviors
        self.revenge_phrases = [
            'make it back', 'need to win', 'must recover', 'lost money',
            'get even', 'make up for', 'recoup losses', 'revenge trade'
        ]

        self.fomo_phrases = [
            'missing out', 'everyone else', 'too late', 'catch up', 'before it\'s gone'
        ]

        self.overtrading_phrases = [
            'one more trade', 'just one more', 'keep going', 'on a roll', 'hot streak'
        ]

        self.gambling_phrases = [
            'bet', 'gamble', 'lucky', 'feeling lucky', 'roll the dice', 'all or nothing'
        ]

        # Urgency indicators
        self.urgency_phrases = [
            'today', 'now', 'quickly', 'fast', 'urgent', 'asap',
            'before close', 'by end of day', 'this morning', 'right now', 'immediately'
        ]

        # Behavioral pattern tracking
        self.behavioral_patterns = {
            'revenge_trading': 0,
            'fomo_trading': 0,
            'overtrading': 0,
            'gambling_behavior': 0,
            'impatience': 0,
            'overconfidence': 0
        }

    def analyze_emotional_state(self, message: str, user_id: str = None) -> EmotionalState:
        """Analyze user's emotional state from message with enhanced behavioral detection"""

        message_lower = message.lower()
        emotions_detected = {}
        warning_flags = []

        # Detect emotions
        for emotion, keywords in self.emotion_keywords.items():
            score = sum(1 for keyword in keywords if keyword in message_lower)
            if score > 0:
                emotions_detected[emotion] = score

        # Determine primary emotion
        if emotions_detected:
            primary_emotion = max(emotions_detected.keys(), key=lambda k: emotions_detected[k])
            confidence = min(emotions_detected[primary_emotion] / 3.0, 1.0)
        else:
            primary_emotion = 'neutral'
            confidence = 0.5

        # Enhanced behavioral pattern detection
        if any(phrase in message_lower for phrase in self.revenge_phrases):
            warning_flags.append('revenge_trading')
            self.behavioral_patterns['revenge_trading'] += 1

        if any(phrase in message_lower for phrase in self.fomo_phrases):
            warning_flags.append('fomo_trading')
            self.behavioral_patterns['fomo_trading'] += 1

        if any(phrase in message_lower for phrase in self.overtrading_phrases):
            warning_flags.append('overtrading')
            self.behavioral_patterns['overtrading'] += 1

        if any(phrase in message_lower for phrase in self.gambling_phrases):
            warning_flags.append('gambling_behavior')
            self.behavioral_patterns['gambling_behavior'] += 1

        # Check for overconfidence patterns
        if primary_emotion == 'overconfident' or 'guaranteed' in message_lower:
            warning_flags.append('overconfident')
            self.behavioral_patterns['overconfidence'] += 1

        # Check urgency and impatience
        urgency_level = 0.0
        if any(phrase in message_lower for phrase in self.urgency_phrases):
            urgency_level = 0.8
            self.behavioral_patterns['impatience'] += 1

        # Pattern-based warnings
        if self.behavioral_patterns['revenge_trading'] >= 3:
            warning_flags.append('chronic_revenge_trading')

        if self.behavioral_patterns['overtrading'] >= 5:
            warning_flags.append('chronic_overtrading')

        # Calculate risk seeking based on emotion and patterns
        risk_seeking = 0.0
        if primary_emotion == 'greedy':
            risk_seeking = 0.7
        elif primary_emotion == 'confident':
            risk_seeking = 0.3
        elif primary_emotion == 'overconfident':
            risk_seeking = 0.9  # Very high risk seeking
        elif primary_emotion == 'fearful':
            risk_seeking = -0.5
        elif primary_emotion == 'anxious':
            risk_seeking = -0.3

        # Adjust risk seeking based on behavioral patterns
        if 'gambling_behavior' in warning_flags:
            risk_seeking = min(risk_seeking + 0.3, 1.0)

        if 'chronic_revenge_trading' in warning_flags:
            risk_seeking = min(risk_seeking + 0.2, 1.0)

        return EmotionalState(
            primary_emotion=primary_emotion,
            confidence_level=confidence,
            risk_seeking=risk_seeking,
            urgency_level=urgency_level,
            warning_flags=warning_flags
        )

    def get_behavioral_summary(self, user_id: str) -> Dict[str, Any]:
        """Get behavioral pattern summary for user"""
        return {
            'patterns': self.behavioral_patterns.copy(),
            'risk_level': self._calculate_behavioral_risk_level(),
            'recommendations': self._get_behavioral_recommendations()
        }

    def _calculate_behavioral_risk_level(self) -> str:
        """Calculate overall behavioral risk level"""
        total_warnings = sum(self.behavioral_patterns.values())

        if total_warnings >= 15:
            return 'high'
        elif total_warnings >= 8:
            return 'moderate'
        elif total_warnings >= 3:
            return 'low'
        else:
            return 'minimal'

    def _get_behavioral_recommendations(self) -> List[str]:
        """Get behavioral coaching recommendations"""
        recommendations = []

        if self.behavioral_patterns['revenge_trading'] >= 3:
            recommendations.append("Consider taking a break after losses to avoid revenge trading")

        if self.behavioral_patterns['overtrading'] >= 5:
            recommendations.append("Set daily trade limits to prevent overtrading")

        if self.behavioral_patterns['fomo_trading'] >= 3:
            recommendations.append("Focus on your trading plan rather than chasing market moves")

        if self.behavioral_patterns['gambling_behavior'] >= 2:
            recommendations.append("Review position sizing and risk management rules")

        if self.behavioral_patterns['overconfidence'] >= 3:
            recommendations.append("Remember that markets are unpredictable - stay humble")

        return recommendations


class GoalParser:
    """Advanced goal and intent parsing"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Goal type patterns
        self.goal_patterns = {
            'profit_target': [
                r'make \$?(\d+)', r'profit \$?(\d+)', r'earn \$?(\d+)',
                r'(\d+) dollars?', r'(\d+) bucks?'
            ],
            'risk_limit': [
                r'risk \$?(\d+)', r'lose \$?(\d+)', r'max loss \$?(\d+)'
            ],
            'learning': [
                r'learn', r'understand', r'explain', r'teach', r'how to'
            ],
            'strategy_test': [
                r'test', r'try', r'experiment', r'see if'
            ]
        }

        # Timeframe patterns
        self.timeframe_patterns = {
            'today': [r'today', r'by end of day', r'before close'],
            'this_week': [r'this week', r'by friday', r'weekly'],
            'this_month': [r'this month', r'monthly'],
            'long_term': [r'long term', r'eventually', r'over time']
        }

    def parse_trading_goal(self, message: str, user_id: str) -> Optional[TradingGoal]:
        """Parse trading goal from user message"""

        message_lower = message.lower()

        # Detect goal type and extract amount
        goal_type = 'learning'  # default
        target_amount = None

        for gtype, patterns in self.goal_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, message_lower)
                if match:
                    goal_type = gtype
                    if match.groups():
                        try:
                            target_amount = float(match.group(1))
                        except (ValueError, IndexError):
                            pass
                    break
            if goal_type != 'learning':
                break

        # Detect timeframe
        timeframe = None
        for tf, patterns in self.timeframe_patterns.items():
            if any(re.search(pattern, message_lower) for pattern in patterns):
                timeframe = tf
                break

        # Detect risk tolerance
        risk_tolerance = self._detect_risk_tolerance(message_lower)

        # Only create goal if we detected a clear intent
        if goal_type != 'learning' or timeframe or target_amount:
            return TradingGoal(
                goal_type=goal_type,
                target_amount=target_amount,
                timeframe=timeframe,
                risk_tolerance=risk_tolerance,
                created_at=datetime.now()
            )

        return None

    def _detect_risk_tolerance(self, message: str) -> str:
        """Detect risk tolerance from message"""
        conservative_keywords = ['safe', 'conservative', 'careful', 'low risk', 'protect', 'secure']
        aggressive_keywords = ['aggressive', 'high risk', 'maximum', 'all in', 'risky', 'bold']

        if any(keyword in message for keyword in conservative_keywords):
            return 'conservative'
        elif any(keyword in message for keyword in aggressive_keywords):
            return 'aggressive'
        else:
            return 'moderate'

    def filter_signals_by_goal(self, signals: List[Dict[str, Any]], goal: TradingGoal) -> List[Dict[str, Any]]:
        """Filter TTM Squeeze signals based on trading goal"""
        if not signals or not goal:
            return signals

        filtered_signals = []

        for signal in signals:
            # Apply risk tolerance filtering
            if self._signal_matches_risk_tolerance(signal, goal.risk_tolerance):
                # Apply timeframe filtering
                if self._signal_matches_timeframe(signal, goal.timeframe):
                    # Apply goal type filtering
                    if self._signal_matches_goal_type(signal, goal.goal_type):
                        filtered_signals.append(signal)

        # Sort by relevance to goal
        return self._sort_signals_by_goal_relevance(filtered_signals, goal)

    def _signal_matches_risk_tolerance(self, signal: Dict[str, Any], risk_tolerance: str) -> bool:
        """Check if signal matches risk tolerance"""
        signal_strength = signal.get('signal_strength', 1)
        confidence_score = signal.get('confidence_score', 0.5)

        if risk_tolerance == 'conservative':
            # Only high-confidence, strong signals
            return signal_strength >= 4 and confidence_score >= 0.8
        elif risk_tolerance == 'aggressive':
            # Accept lower confidence signals
            return signal_strength >= 2 and confidence_score >= 0.4
        else:  # moderate
            # Balanced approach
            return signal_strength >= 3 and confidence_score >= 0.6

    def _signal_matches_timeframe(self, signal: Dict[str, Any], timeframe: Optional[str]) -> bool:
        """Check if signal matches timeframe requirements"""
        if not timeframe:
            return True

        # For now, all TTM signals are suitable for intraday/today goals
        # Could be enhanced with more sophisticated timeframe analysis
        return timeframe in ['today', 'intraday', None]

    def _signal_matches_goal_type(self, signal: Dict[str, Any], goal_type: str) -> bool:
        """Check if signal matches goal type"""
        if goal_type == 'learning':
            return True  # All signals are educational
        elif goal_type == 'profit_target':
            # Prefer stronger signals for profit goals
            return signal.get('signal_strength', 1) >= 3
        elif goal_type == 'strategy_test':
            return True  # All signals suitable for testing
        else:
            return True

    def _sort_signals_by_goal_relevance(self, signals: List[Dict[str, Any]], goal: TradingGoal) -> List[Dict[str, Any]]:
        """Sort signals by relevance to trading goal"""
        def goal_relevance_score(signal):
            score = 0

            # Base score from signal strength
            score += signal.get('signal_strength', 1) * 10

            # Confidence bonus
            score += signal.get('confidence_score', 0.5) * 20

            # Goal-specific bonuses
            if goal.goal_type == 'profit_target' and goal.target_amount:
                # Prefer higher-strength signals for profit goals
                if signal.get('signal_strength', 1) >= 4:
                    score += 15

            if goal.risk_tolerance == 'conservative':
                # Bonus for very high confidence
                if signal.get('confidence_score', 0.5) >= 0.9:
                    score += 10

            return score

        return sorted(signals, key=goal_relevance_score, reverse=True)

    async def get_goal_filtered_signals(self, market_engine, user_goal: Optional[TradingGoal] = None) -> List[Dict[str, Any]]:
        """Get TTM signals filtered by user's trading goal"""
        try:
            # Get live TTM signals from market engine
            live_signals = market_engine.get_live_ttm_signals(min_strength=1)  # Get all signals

            if not live_signals:
                return []

            # Apply goal-based filtering if goal exists
            if user_goal:
                filtered_signals = self.filter_signals_by_goal(live_signals, user_goal)
                self.logger.info(f"Filtered {len(live_signals)} signals to {len(filtered_signals)} based on goal: {user_goal.goal_type}")
                return filtered_signals
            else:
                # No goal - return top signals by strength
                return sorted(live_signals, key=lambda x: x.get('signal_strength', 1), reverse=True)[:10]

        except Exception as e:
            self.logger.error(f"Error getting goal-filtered signals: {e}")
            return []


class ChainOfThoughtEngine:
    """Core reasoning engine with step-by-step analysis"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def analyze_trade_setup(self, symbol: str, quote: Quote, 
                                 indicators: TechnicalIndicators,
                                 market_context: Dict[str, Any]) -> ChainOfThoughtAnalysis:
        """Perform comprehensive chain-of-thought analysis"""
        
        steps = []
        confidence_factors = []
        
        # Step 1: Market Context Analysis
        market_step = await self._analyze_market_context(market_context)
        steps.append(market_step)
        confidence_factors.append(market_step.get('confidence', 0.5))
        
        # Step 2: Technical Analysis
        technical_step = await self._analyze_technical_setup(symbol, quote, indicators)
        steps.append(technical_step)
        confidence_factors.append(technical_step.get('confidence', 0.5))
        
        # Step 3: Risk Assessment
        risk_step = await self._analyze_risk_factors(symbol, quote, indicators)
        steps.append(risk_step)
        confidence_factors.append(risk_step.get('confidence', 0.5))
        
        # Step 4: Entry/Exit Strategy
        strategy_step = await self._develop_entry_exit_strategy(symbol, quote, indicators)
        steps.append(strategy_step)
        confidence_factors.append(strategy_step.get('confidence', 0.5))
        
        # Calculate final confidence
        final_confidence = sum(confidence_factors) / len(confidence_factors)
        
        # Generate educational explanation
        explanation = self._generate_educational_explanation(steps, final_confidence)
        
        return ChainOfThoughtAnalysis(
            symbol=symbol,
            analysis_steps=steps,
            final_confidence=final_confidence,
            recommendation="BUY" if final_confidence > 0.7 else "HOLD" if final_confidence > 0.4 else "AVOID",
            reasoning=explanation,
            risk_factors=[step.get('risks', []) for step in steps],
            educational_notes=[step.get('education', '') for step in steps],
            timestamp=datetime.utcnow()
        )
    
    async def _analyze_market_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze overall market conditions"""
        return {
            "step": "Market Context Analysis",
            "analysis": "Evaluating overall market conditions and sentiment",
            "confidence": 0.6,
            "education": "Market context helps determine if it's a good time to trade"
        }
    
    async def _analyze_technical_setup(self, symbol: str, quote: Quote, 
                                     indicators: TechnicalIndicators) -> Dict[str, Any]:
        """Analyze technical indicators and patterns"""
        return {
            "step": "Technical Analysis",
            "analysis": f"Analyzing {symbol} technical indicators and price action",
            "confidence": 0.7,
            "education": "Technical analysis helps identify entry and exit points"
        }
    
    async def _analyze_risk_factors(self, symbol: str, quote: Quote,
                                  indicators: TechnicalIndicators) -> Dict[str, Any]:
        """Assess risk factors and position sizing"""
        return {
            "step": "Risk Assessment",
            "analysis": "Evaluating potential risks and appropriate position size",
            "confidence": 0.8,
            "risks": ["Market volatility", "Sector risk"],
            "education": "Risk management is the most important aspect of trading"
        }
    
    async def _develop_entry_exit_strategy(self, symbol: str, quote: Quote,
                                         indicators: TechnicalIndicators) -> Dict[str, Any]:
        """Develop entry and exit strategy"""
        return {
            "step": "Entry/Exit Strategy",
            "analysis": "Determining optimal entry price, stop loss, and profit targets",
            "confidence": 0.6,
            "education": "Having a clear plan before entering helps maintain discipline"
        }
    
    def _generate_educational_explanation(self, steps: List[Dict], confidence: float) -> str:
        """Generate beginner-friendly explanation"""
        explanation = "Here's my step-by-step analysis:\n\n"
        
        for i, step in enumerate(steps, 1):
            explanation += f"{i}. {step['step']}: {step['analysis']}\n"
            if 'education' in step:
                explanation += f"   💡 {step['education']}\n\n"
        
        explanation += f"Overall Confidence: {confidence*100:.0f}%\n"
        
        if confidence > 0.7:
            explanation += "This looks like a strong setup with good risk/reward potential."
        elif confidence > 0.4:
            explanation += "This is a moderate setup - proceed with caution and smaller position size."
        else:
            explanation += "This setup has too many risks - better to wait for a clearer opportunity."
        
        return explanation


class MultiAgentCoordinator:
    """Coordinates multiple specialized trading agents"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.agent_weights = {
            AgentType.TECHNICAL: 0.35,
            AgentType.RISK: 0.30,
            AgentType.SENTIMENT: 0.20,
            AgentType.EXECUTION: 0.15
        }
    
    async def get_consensus_analysis(self, symbol: str, quote: Quote,
                                   indicators: TechnicalIndicators,
                                   market_context: Dict[str, Any]) -> AgentConsensus:
        """Get consensus from all agents"""
        
        agent_analyses = {}
        
        # Technical Analysis Agent
        agent_analyses[AgentType.TECHNICAL] = await self._technical_agent_analysis(
            symbol, quote, indicators
        )
        
        # Risk Management Agent
        agent_analyses[AgentType.RISK] = await self._risk_agent_analysis(
            symbol, quote, indicators
        )
        
        # Sentiment Analysis Agent
        agent_analyses[AgentType.SENTIMENT] = await self._sentiment_agent_analysis(
            symbol, market_context
        )
        
        # Execution Timing Agent
        agent_analyses[AgentType.EXECUTION] = await self._execution_agent_analysis(
            symbol, quote, market_context
        )
        
        # Calculate weighted consensus
        weighted_decision = sum(
            analysis.decision * self.agent_weights[agent_type]
            for agent_type, analysis in agent_analyses.items()
        )
        
        # Calculate consensus confidence
        consensus_confidence = sum(
            analysis.confidence * self.agent_weights[agent_type]
            for agent_type, analysis in agent_analyses.items()
        )
        
        # Calculate disagreement score
        decisions = [analysis.decision for analysis in agent_analyses.values()]
        disagreement_score = max(decisions) - min(decisions)
        
        return AgentConsensus(
            final_decision=weighted_decision,
            consensus_confidence=consensus_confidence,
            agent_votes=agent_analyses,
            disagreement_score=disagreement_score,
            risk_assessment=self._generate_risk_assessment(agent_analyses),
            execution_recommendation=self._generate_execution_recommendation(
                weighted_decision, consensus_confidence, disagreement_score
            )
        )
    
    async def _technical_agent_analysis(self, symbol: str, quote: Quote,
                                      indicators: TechnicalIndicators) -> AgentAnalysis:
        """Technical analysis agent"""
        # Simplified technical analysis
        decision = 0.5  # Placeholder
        confidence = 0.7
        
        return AgentAnalysis(
            agent_type=AgentType.TECHNICAL,
            decision=decision,
            confidence=confidence,
            reasoning="Technical indicators show moderate bullish signals",
            data_points={"rsi": indicators.rsi, "macd": indicators.macd_signal},
            risk_factors=["Overbought conditions"],
            timestamp=datetime.utcnow()
        )
    
    async def _risk_agent_analysis(self, symbol: str, quote: Quote,
                                 indicators: TechnicalIndicators) -> AgentAnalysis:
        """Risk management agent"""
        decision = 0.3  # Conservative
        confidence = 0.8
        
        return AgentAnalysis(
            agent_type=AgentType.RISK,
            decision=decision,
            confidence=confidence,
            reasoning="Risk factors are manageable with proper position sizing",
            data_points={"volatility": indicators.atr, "volume": quote.volume},
            risk_factors=["Market volatility", "Position concentration"],
            timestamp=datetime.utcnow()
        )
    
    async def _sentiment_agent_analysis(self, symbol: str,
                                      market_context: Dict[str, Any]) -> AgentAnalysis:
        """Sentiment analysis agent"""
        decision = 0.4
        confidence = 0.6
        
        return AgentAnalysis(
            agent_type=AgentType.SENTIMENT,
            decision=decision,
            confidence=confidence,
            reasoning="Market sentiment is neutral to slightly positive",
            data_points={"news_sentiment": 0.1, "social_sentiment": 0.2},
            risk_factors=["Mixed sentiment signals"],
            timestamp=datetime.utcnow()
        )
    
    async def _execution_agent_analysis(self, symbol: str, quote: Quote,
                                      market_context: Dict[str, Any]) -> AgentAnalysis:
        """Execution timing agent"""
        decision = 0.6
        confidence = 0.7
        
        return AgentAnalysis(
            agent_type=AgentType.EXECUTION,
            decision=decision,
            confidence=confidence,
            reasoning="Good execution conditions with adequate liquidity",
            data_points={"spread": quote.bid_ask_spread, "volume": quote.volume},
            risk_factors=["Market hours", "Liquidity"],
            timestamp=datetime.utcnow()
        )
    
    def _generate_risk_assessment(self, analyses: Dict[AgentType, AgentAnalysis]) -> str:
        """Generate overall risk assessment"""
        risk_factors = []
        for analysis in analyses.values():
            risk_factors.extend(analysis.risk_factors)
        
        unique_risks = list(set(risk_factors))
        return f"Key risks: {', '.join(unique_risks[:3])}"
    
    def _generate_execution_recommendation(self, decision: float, confidence: float,
                                         disagreement: float) -> str:
        """Generate execution recommendation"""
        if confidence > 0.7 and disagreement < 0.3:
            return "EXECUTE - Strong consensus with high confidence"
        elif confidence > 0.5 and disagreement < 0.5:
            return "PROCEED WITH CAUTION - Moderate consensus"
        else:
            return "WAIT - Low consensus or high disagreement among agents"


class AIValidationSystem:
    """AI response validation and grounding system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def validate_trading_response(self, response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Validate AI trading response for accuracy and safety"""
        
        validation_result = {
            "is_valid": True,
            "confidence": 0.8,
            "warnings": [],
            "corrections": [],
            "safety_flags": []
        }
        
        # Check for unrealistic claims
        if self._contains_unrealistic_claims(response):
            validation_result["warnings"].append("Response contains potentially unrealistic claims")
            validation_result["confidence"] *= 0.8
        
        # Check for proper risk disclaimers
        if not self._has_risk_disclaimers(response):
            validation_result["warnings"].append("Missing appropriate risk disclaimers")
            validation_result["corrections"].append("Add risk management reminders")
        
        # Check for educational content
        if not self._has_educational_content(response):
            validation_result["warnings"].append("Could benefit from more educational content")
        
        return validation_result
    
    def _contains_unrealistic_claims(self, response: str) -> bool:
        """Check for unrealistic trading claims"""
        unrealistic_patterns = [
            r"guaranteed.*profit",
            r"100%.*success",
            r"risk.*free",
            r"always.*profitable"
        ]
        
        for pattern in unrealistic_patterns:
            if re.search(pattern, response.lower()):
                return True
        return False
    
    def _has_risk_disclaimers(self, response: str) -> bool:
        """Check if response includes appropriate risk warnings"""
        risk_keywords = ["risk", "loss", "careful", "caution", "paper trading"]
        return any(keyword in response.lower() for keyword in risk_keywords)
    
    def _has_educational_content(self, response: str) -> bool:
        """Check if response includes educational elements"""
        educational_keywords = ["because", "reason", "why", "how", "learn", "understand"]
        return any(keyword in response.lower() for keyword in educational_keywords)


class ConversationalFlowEngine:
    """Enhanced conversational flow with clarification and dialog trees"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def needs_clarification(self, message: str, context: Dict[str, Any]) -> Optional[str]:
        """Determine if message needs clarification"""

        message_lower = message.lower()

        # Check for ambiguous requests
        ambiguous_patterns = [
            (r'\bmake money\b', "How much would you like to make and by when?"),
            (r'\bbest stock\b', "Best for what timeframe? Day trading, swing trading, or long-term?"),
            (r'\bsafe\b.*\btrade\b', "What's your definition of 'safe'? Low volatility or small position size?"),
            (r'\bquick\b.*\bprofit\b', "How quick? Today, this week, or within a month?"),
            (r'\bgood\b.*\bentry\b', "Good entry for which strategy? Momentum, mean reversion, or breakout?")
        ]

        for pattern, clarification in ambiguous_patterns:
            if re.search(pattern, message_lower):
                return clarification

        # Check if symbol mentioned but no clear action
        if self._has_symbol_but_no_action(message):
            return "What would you like me to analyze about this stock? Technical setup, risk assessment, or entry/exit points?"

        return None

    def _has_symbol_but_no_action(self, message: str) -> bool:
        """Check if message has symbol but no clear action"""
        # Simple check for stock symbols without action words
        has_symbol = bool(re.search(r'\b[A-Z]{1,5}\b', message))
        action_words = ['analyze', 'buy', 'sell', 'trade', 'look at', 'check', 'what about']
        has_action = any(word in message.lower() for word in action_words)

        return has_symbol and not has_action

    def generate_follow_up_suggestions(self, context: Dict[str, Any]) -> List[str]:
        """Generate follow-up suggestions based on context"""

        suggestions = []

        # Based on conversation state
        state = context.get('conversation_state', 'greeting')

        if state == 'analyzing':
            suggestions.extend([
                "Would you like me to scan for more opportunities?",
                "Should I explain this setup in more detail?",
                "Want to see the risk assessment for this trade?"
            ])

        elif state == 'confirming':
            suggestions.extend([
                "Shall I proceed with this trade recommendation?",
                "Would you prefer a smaller position size?",
                "Want to see alternative setups?"
            ])

        # Based on user goal
        goal = context.get('current_goal')
        if goal and goal.get('goal_type') == 'profit_target':
            suggestions.append(f"Want me to find trades to help reach your ${goal.get('target_amount', 0)} goal?")

        return suggestions[:3]  # Limit to 3 suggestions

    def should_confirm_trade(self, confidence: float, risk_level: str, emotional_state: EmotionalState) -> bool:
        """Determine if trade needs confirmation"""

        # Always confirm if confidence is low
        if confidence < 0.7:
            return True

        # Confirm if user shows warning signs
        if emotional_state.warning_flags:
            return True

        # Confirm if high risk
        if risk_level in ['high', 'extreme']:
            return True

        # Confirm if user is urgent/emotional
        if emotional_state.urgency_level > 0.6:
            return True

        return False


class GroundingEngine:
    """Fact-checking and grounding engine to prevent hallucinations"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Trading book quotes for grounding
        self.book_quotes = {
            'risk_management': {
                'source': 'Trading in the Zone - Mark Douglas',
                'quote': 'The best traders predefine their risk before entering any trade.'
            },
            'psychology': {
                'source': 'Trading in the Zone - Mark Douglas',
                'quote': 'The biggest challenge traders face is not market analysis, but managing their own psychology.'
            },
            'cutting_losses': {
                'source': 'Market Wizards - Ed Seykota',
                'quote': 'The elements of good trading are cutting losses, cutting losses, and cutting losses.'
            },
            'position_sizing': {
                'source': 'Market Wizards - Michael Marcus',
                'quote': 'I never risk more than 1-2% of my total equity on any trade.'
            }
        }

    def ground_response(self, response: str, topic: str) -> str:
        """Add grounding sources to response"""

        # Find relevant book quote
        relevant_quote = None
        for key, quote_data in self.book_quotes.items():
            if key in topic.lower():
                relevant_quote = quote_data
                break

        if relevant_quote:
            response += f"\n\n📚 **{relevant_quote['source']}**: \"{relevant_quote['quote']}\""

        return response

    def validate_trading_facts(self, claims: List[str]) -> Dict[str, bool]:
        """Validate trading-related factual claims"""

        validated = {}

        for claim in claims:
            claim_lower = claim.lower()

            # Basic fact checking
            if 'guaranteed' in claim_lower and 'profit' in claim_lower:
                validated[claim] = False  # No guaranteed profits
            elif 'risk free' in claim_lower:
                validated[claim] = False  # No risk-free trading
            elif '100%' in claim_lower and ('win rate' in claim_lower or 'success' in claim_lower):
                validated[claim] = False  # No 100% win rates
            else:
                validated[claim] = True  # Assume valid if no red flags

        return validated


class AtlasAIEngine:
    """Main AI Engine coordinating all AI functionality with enhanced conversational intelligence"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Core engines
        self.cot_engine = ChainOfThoughtEngine()
        self.multi_agent = MultiAgentCoordinator()
        self.validation = AIValidationSystem()

        # Enhanced conversational intelligence
        self.context_memory = EnhancedContextMemory()
        self.emotional_intelligence = EmotionalIntelligence()
        self.goal_parser = GoalParser()
        self.conversation_flow = ConversationalFlowEngine()
        self.grounding_engine = GroundingEngine()

        # Configuration
        self.communication_mode = CommunicationMode.MENTOR

        # Initialize OpenAI
        openai.api_key = settings.OPENAI_API_KEY
    
    @performance_optimizer.performance_monitor("process_trading_query")
    async def process_trading_query(self, message: str, context: Dict[str, Any]) -> AIResponse:
        """Process trading query with enhanced conversational intelligence"""

        try:
            # Get user context
            session_id = context.get('session_id', 'default')
            user_id = context.get('user_id', 'default_user')

            # Analyze emotional state with behavioral tracking
            emotional_state = self.emotional_intelligence.analyze_emotional_state(message, user_id)

            # Store behavioral patterns if detected
            for warning in emotional_state.warning_flags:
                severity = 3 if 'chronic' in warning else 1
                self.context_memory.store_behavioral_pattern(user_id, warning, severity, message[:100])

            # Get user preferences, behavioral history, and session continuity
            user_preferences = self.context_memory.get_user_preferences(user_id)
            behavioral_history = self.context_memory.get_behavioral_history(user_id)
            session_continuity = self.context_memory.get_session_continuity(user_id, session_id)

            # Parse potential trading goal
            trading_goal = self.goal_parser.parse_trading_goal(message, user_id)
            if trading_goal:
                goal_id = self.context_memory.store_trading_goal(user_id, trading_goal)
                self.logger.info(f"New trading goal detected: {trading_goal.goal_type}")

                # Store goal in enhanced context for filtering
                enhanced_context['current_trading_goal'] = trading_goal

            # Get active goal
            active_goal = self.context_memory.get_active_goal(user_id)

            # Check if clarification is needed
            clarification = self.conversation_flow.needs_clarification(message, context)
            if clarification:
                return AIResponse(
                    response=clarification,
                    type="clarification_request",
                    confidence=0.9,
                    requires_action=False,
                    timestamp=datetime.now()
                )

            # Extract intent and symbols
            intent = self._classify_intent(message)
            symbol = self._extract_symbol(message)

            # Enhanced context with emotional and goal awareness
            enhanced_context = {
                **context,
                'emotional_state': emotional_state,
                'user_preferences': user_preferences,
                'behavioral_history': behavioral_history,
                'session_continuity': session_continuity,
                'active_goal': active_goal,
                'user_id': user_id,
                'session_id': session_id
            }

            # Store session continuity for next interaction
            current_topic = self._extract_topic(message)
            self.context_memory.store_session_continuity(
                user_id, session_id, 'processing', current_topic,
                {'last_message': message[:200], 'emotional_state': emotional_state.primary_emotion}
            )

            # Process based on intent and context
            if symbol and 'quote' in context:
                # Perform chain-of-thought analysis
                cot_analysis = await self.cot_engine.analyze_trade_setup(
                    symbol, context['quote'], context.get('indicators'), enhanced_context
                )

                # Get multi-agent consensus
                consensus = await self.multi_agent.get_consensus_analysis(
                    symbol, context['quote'], context.get('indicators'), enhanced_context
                )

                # Check if confirmation is needed
                needs_confirmation = self.conversation_flow.should_confirm_trade(
                    consensus.consensus_confidence,
                    'moderate',  # Would get from risk assessment
                    emotional_state
                )

                # Generate AI response with emotional awareness
                response_text = await self._generate_enhanced_response(
                    message, intent, cot_analysis, consensus, enhanced_context, needs_confirmation
                )
            else:
                # General trading question with emotional awareness
                response_text = await self._generate_enhanced_general_response(
                    message, intent, enhanced_context
                )

            # Apply grounding to prevent hallucinations
            response_text = self.grounding_engine.ground_response(response_text, intent)

            # Add follow-up suggestions
            follow_ups = self.conversation_flow.generate_follow_up_suggestions(enhanced_context)
            if follow_ups:
                response_text += f"\n\n💡 **What's next?**\n" + "\n".join(f"• {suggestion}" for suggestion in follow_ups)

            # Validate response
            validation = await self.validation.validate_trading_response(response_text, enhanced_context)

            # Apply any corrections
            if validation['corrections']:
                response_text = await self._apply_corrections(response_text, validation['corrections'])

            # Add emotional coaching if needed
            if emotional_state.warning_flags:
                response_text = self._add_emotional_coaching(response_text, emotional_state)

            return AIResponse(
                response=response_text,
                type=intent,
                confidence=validation['confidence'],
                requires_action=intent in ['trade_request', 'analysis_request'],
                emotional_state=asdict(emotional_state),
                active_goal=asdict(active_goal) if active_goal else None,
                timestamp=datetime.now()
            )

        except Exception as e:
            self.logger.error(f"Error processing trading query: {e}")
            return AIResponse(
                response="I apologize, but I encountered an error processing your request. Please try again.",
                type="error",
                confidence=0.0,
                timestamp=datetime.now()
            )
    
    def _classify_intent(self, message: str) -> str:
        """Classify user intent from message"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ['buy', 'sell', 'trade', 'position']):
            return 'trade_request'
        elif any(word in message_lower for word in ['analyze', 'analysis', 'chart', 'technical']):
            return 'analysis_request'
        elif any(word in message_lower for word in ['learn', 'explain', 'what is', 'how to']):
            return 'education_request'
        elif any(word in message_lower for word in ['portfolio', 'performance', 'profit']):
            return 'portfolio_request'
        else:
            return 'general_chat'
    
    def _extract_symbol(self, message: str) -> Optional[str]:
        """Extract stock symbol from message"""
        # Simple regex for stock symbols
        import re
        pattern = r'\b[A-Z]{1,5}\b'
        matches = re.findall(pattern, message.upper())
        
        # Filter out common words that aren't symbols
        common_words = {'THE', 'AND', 'OR', 'BUT', 'FOR', 'AT', 'TO', 'FROM', 'UP', 'DOWN', 'IN', 'OUT', 'ON', 'OFF', 'OVER', 'UNDER', 'AGAIN', 'FURTHER', 'THEN', 'ONCE'}
        symbols = [match for match in matches if match not in common_words and len(match) <= 5]
        
        return symbols[0] if symbols else None

    def _extract_topic(self, message: str) -> str:
        """Extract main topic from message for session continuity"""
        message_lower = message.lower()

        # Trading topics
        if any(word in message_lower for word in ['analyze', 'analysis', 'chart']):
            return 'analysis'
        elif any(word in message_lower for word in ['buy', 'sell', 'trade']):
            return 'trading'
        elif any(word in message_lower for word in ['risk', 'stop', 'loss']):
            return 'risk_management'
        elif any(word in message_lower for word in ['learn', 'explain', 'what is']):
            return 'education'
        elif any(word in message_lower for word in ['goal', 'target', 'make money']):
            return 'goal_setting'
        else:
            return 'general_chat'
    
    async def _generate_contextual_response(self, message: str, intent: str,
                                          cot_analysis: ChainOfThoughtAnalysis,
                                          consensus: AgentConsensus) -> str:
        """Generate contextual response with trading analysis"""
        
        prompt = f"""
        As A.T.L.A.S, an AI trading mentor, respond to this user message: "{message}"
        
        Chain-of-Thought Analysis:
        - Symbol: {cot_analysis.symbol}
        - Confidence: {cot_analysis.final_confidence*100:.0f}%
        - Recommendation: {cot_analysis.recommendation}
        - Reasoning: {cot_analysis.reasoning}
        
        Multi-Agent Consensus:
        - Final Decision: {consensus.final_decision:.2f}
        - Consensus Confidence: {consensus.consensus_confidence*100:.0f}%
        - Risk Assessment: {consensus.risk_assessment}
        - Execution Recommendation: {consensus.execution_recommendation}
        
        Respond in a {self.communication_mode.value} style with:
        1. Clear explanation of the analysis
        2. Educational insights for beginners
        3. Appropriate risk warnings
        4. Actionable recommendations
        
        Remember: This is paper trading for educational purposes.
        """
        
        try:
            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=500,
                temperature=0.7
            )
            return response.choices[0].message.content
        except Exception as e:
            self.logger.error(f"OpenAI API error: {e}")
            return f"Based on my analysis of {cot_analysis.symbol}, I see {cot_analysis.recommendation.lower()} signals with {cot_analysis.final_confidence*100:.0f}% confidence. {cot_analysis.reasoning}"
    
    async def _generate_general_response(self, message: str, intent: str, context: Dict[str, Any]) -> str:
        """Generate general trading response"""
        
        prompt = f"""
        As A.T.L.A.S, an AI trading mentor, respond to: "{message}"
        
        Intent: {intent}
        Communication Style: {self.communication_mode.value}
        
        Provide helpful, educational trading guidance with appropriate risk warnings.
        Remember: This is for paper trading and educational purposes only.
        """
        
        try:
            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=400,
                temperature=0.7
            )
            return response.choices[0].message.content
        except Exception as e:
            self.logger.error(f"OpenAI API error: {e}")
            return "I'm here to help you learn about trading! What specific topic would you like to explore?"
    
    async def _generate_enhanced_response(self, message: str, intent: str,
                                        cot_analysis: ChainOfThoughtAnalysis,
                                        consensus: AgentConsensus,
                                        context: Dict[str, Any],
                                        needs_confirmation: bool) -> str:
        """Generate enhanced response with emotional and goal awareness"""

        emotional_state = context.get('emotional_state')
        active_goal = context.get('active_goal')
        user_level = context.get('user_profile', {}).get('experience_level', 'beginner')

        # Start with mentor-style greeting
        response = f"👋 **Let's analyze {cot_analysis.symbol} together!**\n\n"

        # Use beginner-friendly language for recommendation
        recommendation_emoji = "🟢" if "BUY" in cot_analysis.recommendation.upper() else "🔴" if "SELL" in cot_analysis.recommendation.upper() else "🟡"
        response += f"{recommendation_emoji} **My Take:** {self._translate_recommendation_to_friendly(cot_analysis.recommendation)}\n"

        # Confidence with analogy
        confidence_pct = cot_analysis.final_confidence * 100
        confidence_analogy = self._get_confidence_analogy(confidence_pct)
        response += f"🎯 **Confidence:** {confidence_pct:.0f}% {confidence_analogy}\n\n"

        # Reasoning with educational context
        response += f"🧠 **Here's my thinking:** {self._enhance_reasoning_with_analogies(cot_analysis.reasoning, user_level)}\n\n"

        # Multi-agent consensus in simple terms
        response += f"🤖 **Team Analysis:** {self._simplify_consensus(consensus)}\n\n"

        # Emotional coaching if needed
        if emotional_state and emotional_state.warning_flags:
            if 'revenge_trading' in emotional_state.warning_flags:
                response += "⚠️ **Emotional Check:** I notice you might be trying to recover losses. Remember, revenge trading often leads to bigger losses. Let's focus on high-probability setups instead.\n\n"

        # Goal progress if applicable
        if active_goal and active_goal.goal_type == 'profit_target':
            response += f"🎯 **Goal Progress:** Working toward your ${active_goal.target_amount} target"
            if active_goal.timeframe:
                response += f" {active_goal.timeframe}"
            response += f" (Progress: {active_goal.progress*100:.0f}%)\n\n"

        # Confirmation if needed
        if needs_confirmation:
            response += "🤔 **Confirmation Required:** This trade has some risk factors. Are you sure you want to proceed? I can also show you safer alternatives.\n\n"

        # Complexity adjustment
        complexity = context.get('complexity_level', 'beginner')
        if complexity == 'beginner':
            response += "💡 **For Beginners:** This analysis combines technical indicators with risk management. The confidence score tells you how strong the signal is."

        return response

    def _translate_recommendation_to_friendly(self, recommendation: str) -> str:
        """Convert technical recommendation to friendly language"""
        rec_lower = recommendation.lower()

        if 'strong buy' in rec_lower:
            return "This looks like a great opportunity! The signals are very positive."
        elif 'buy' in rec_lower:
            return "I'm seeing some good signals here. This could be worth considering."
        elif 'strong sell' in rec_lower:
            return "I'd be very cautious here. The signals suggest staying away."
        elif 'sell' in rec_lower:
            return "The signals aren't looking great. I'd probably pass on this one."
        elif 'hold' in rec_lower:
            return "This one's sitting on the fence. Not bad, but not exciting either."
        else:
            return "Let me break down what I'm seeing in the charts..."

    def _get_confidence_analogy(self, confidence_pct: float) -> str:
        """Get confidence analogy for better understanding"""
        if confidence_pct >= 90:
            return "(Like a sunny day - very clear signals!)"
        elif confidence_pct >= 80:
            return "(Like mostly sunny skies - strong signals)"
        elif confidence_pct >= 70:
            return "(Like partly cloudy - decent signals)"
        elif confidence_pct >= 60:
            return "(Like overcast - mixed signals)"
        elif confidence_pct >= 50:
            return "(Like light rain - weak signals)"
        else:
            return "(Like a storm - very unclear signals)"

    def _enhance_reasoning_with_analogies(self, reasoning: str, user_level: str) -> str:
        """Enhance reasoning with analogies and educational context"""
        if user_level == 'beginner':
            # Add analogies for common trading concepts
            enhanced = reasoning

            # TTM Squeeze analogies
            if 'ttm squeeze' in reasoning.lower():
                enhanced += "\n\n💡 *Think of TTM Squeeze like a coiled spring - when it's compressed (squeezed), it builds up energy. When it releases, the price can move strongly in one direction.*"

            # Volume analogies
            if 'volume' in reasoning.lower():
                enhanced += "\n\n💡 *Volume is like the crowd at a concert - when everyone's excited and participating, the energy (price movement) is much stronger.*"

            # Support/Resistance analogies
            if 'support' in reasoning.lower() or 'resistance' in reasoning.lower():
                enhanced += "\n\n💡 *Support and resistance are like floors and ceilings in a building - price tends to bounce off these levels.*"

            return enhanced
        else:
            return reasoning

    def _simplify_consensus(self, consensus: AgentConsensus) -> str:
        """Simplify multi-agent consensus for beginners"""
        simplified = "My AI team analyzed this from different angles:\n"
        simplified += f"• **Technical Expert:** {self._simplify_technical_view(consensus.execution_recommendation)}\n"
        simplified += f"• **Risk Manager:** {self._simplify_risk_view(consensus.risk_assessment)}\n"
        simplified += f"• **Market Sentiment:** {self._get_sentiment_summary()}"

        return simplified

    def _simplify_technical_view(self, recommendation: str) -> str:
        """Simplify technical recommendation"""
        if 'buy' in recommendation.lower():
            return "Charts look good for an upward move"
        elif 'sell' in recommendation.lower():
            return "Charts suggest downward pressure"
        else:
            return "Charts are showing mixed signals"

    def _simplify_risk_view(self, risk_assessment: str) -> str:
        """Simplify risk assessment"""
        if 'low' in risk_assessment.lower():
            return "Risk looks manageable with proper position sizing"
        elif 'high' in risk_assessment.lower():
            return "Higher risk - would need smaller position size"
        else:
            return "Moderate risk - standard position sizing applies"

    def _get_sentiment_summary(self) -> str:
        """Get simplified sentiment summary"""
        return "Market mood is neutral to slightly positive"

    def _generate_anxiety_coaching_response(self, active_goal) -> str:
        """Generate coaching response for anxious traders"""
        response = "🤗 **Hey, I totally get it!** Trading anxiety is like learning to drive - everyone feels nervous at first.\n\n"
        response += "💡 **Here's what helps:**\n"
        response += "• Start with paper trading (like a flight simulator for trading)\n"
        response += "• Think of your first trades as 'tuition' for learning\n"
        response += "• Remember: even Warren Buffett had his first trade!\n\n"

        if active_goal and active_goal.target_amount:
            response += f"🎯 I see you want to make ${active_goal.target_amount}. Let's break that into smaller, less scary steps. "
            response += "What if we aimed for just 10% of that first?\n\n"

        response += "🚀 **Ready to start small?** Pick a stock you know well (like Apple or Microsoft) and let's analyze it together!"
        return response

    def _generate_greed_coaching_response(self, active_goal) -> str:
        """Generate coaching response for greedy behavior"""
        response = "🎯 **I love the enthusiasm!** But here's a secret from successful traders:\n\n"
        response += "💰 **The tortoise beats the hare in trading.** Think of it like building muscle at the gym - "
        response += "you can't bench press 300 pounds on day one, but with consistent training, you'll get there.\n\n"
        response += "🧠 **Smart traders think like this:**\n"
        response += "• 'How can I NOT lose money?' (before thinking about profits)\n"
        response += "• 'What's my exit plan if I'm wrong?'\n"
        response += "• 'Am I risking money I can afford to lose?'\n\n"

        if active_goal and active_goal.target_amount:
            daily_target = active_goal.target_amount / 30 if active_goal.timeframe == 'this_month' else active_goal.target_amount
            response += f"🎯 Instead of chasing ${active_goal.target_amount} all at once, what if we aimed for "
            response += f"${daily_target:.0f} consistently? Small wins compound into big victories!\n\n"

        response += "🤝 **Let's find a high-probability setup together.** What's your account size so I can suggest safe position sizes?"
        return response

    def _generate_revenge_trading_coaching(self) -> str:
        """Generate coaching response for revenge trading"""
        response = "🛑 **Whoa there, partner!** I can sense you might be trying to 'get even' with the market.\n\n"
        response += "🥊 **Here's the thing about revenge trading:** It's like trying to punch back at a boxing ring - "
        response += "the market doesn't care about your feelings, and you'll just hurt yourself more.\n\n"
        response += "🧘 **What the pros do instead:**\n"
        response += "• Take a 15-minute break (seriously, set a timer)\n"
        response += "• Remember: losses are just 'tuition' for learning\n"
        response += "• Focus on the NEXT good trade, not the last bad one\n\n"
        response += "💡 **Think of it like this:** A surgeon doesn't operate angry, and a pilot doesn't fly emotional. "
        response += "Trading requires the same cool head.\n\n"
        response += "🎯 **Ready to reset?** Let's find a high-probability setup with proper risk management. "
        response += "What's your account size so I can suggest a safe position size?"
        return response

    async def _generate_enhanced_general_response(self, message: str, intent: str,
                                                context: Dict[str, Any]) -> str:
        """Generate enhanced general response with emotional awareness"""

        emotional_state = context.get('emotional_state')
        active_goal = context.get('active_goal')
        user_level = context.get('user_profile', {}).get('experience_level', 'beginner')

        # Handle emotional states with mentor-style coaching
        if emotional_state:
            if emotional_state.primary_emotion == 'anxious':
                return self._generate_anxiety_coaching_response(active_goal)

            elif emotional_state.primary_emotion == 'greedy':
                return self._generate_greed_coaching_response(active_goal)

            elif 'revenge_trading' in emotional_state.warning_flags:
                return self._generate_revenge_trading_coaching()

        # Check for goal-filtered signals to proactively suggest
        goal_filtered_signals = context.get('goal_filtered_signals', [])

        # Handle based on intent
        if intent == 'education_request':
            response = "🎓 **I'm here to help you learn!** What trading concept would you like me to explain?\n\n"
            response += "📚 **I can teach you about:**\n"
            response += "• Technical analysis (reading charts like a pro)\n"
            response += "• Risk management (protecting your money)\n"
            response += "• Trading psychology (mastering your emotions)\n"
            response += "• Specific strategies (TTM Squeeze, momentum, etc.)\n\n"
            response += "💡 **Pro tip:** The best way to learn is by analyzing real examples together!"
            return response

        elif intent == 'general_chat':
            base_response = "👋 **Hey there! I'm A.T.L.A.S, your AI trading mentor!**\n\n"
            base_response += "🎯 I'm here to help you learn trading through safe paper trading practice. "
            base_response += "Think of me as your patient trading coach who never gets tired of explaining things!\n\n"

            if active_goal:
                base_response += f"🎯 I see you're working toward a **{active_goal.goal_type}**"
                if active_goal.target_amount:
                    base_response += f" of **${active_goal.target_amount}**"
                if active_goal.timeframe:
                    base_response += f" **{active_goal.timeframe}**"
                base_response += ". That's a great goal!\n\n"

                # Suggest goal-filtered signals if available
                if goal_filtered_signals:
                    base_response += f"🎯 **Good news!** I found {len(goal_filtered_signals)} TTM Squeeze signals that match your goal. "
                    base_response += "Want me to show you the best ones?\n\n"

                base_response += "How can I help you work toward that goal today?"
            else:
                base_response += "🚀 **What would you like to explore today?**\n"
                base_response += "• Analyze a specific stock\n"
                base_response += "• Learn trading concepts\n"
                base_response += "• Set up a trading goal\n"
                base_response += "• Find trading opportunities\n\n"

                # Suggest signals even without a goal
                if goal_filtered_signals:
                    base_response += f"💡 **By the way,** I'm seeing {len(goal_filtered_signals)} interesting TTM Squeeze signals right now. "
                    base_response += "Want to take a look?"

            return base_response

        return "🤝 **I'm here to help with your trading journey!** What would you like to explore? I can analyze stocks, teach concepts, or help you find opportunities."

    def _add_emotional_coaching(self, response: str, emotional_state: EmotionalState) -> str:
        """Add emotional coaching based on detected state"""

        coaching = "\n\n🧠 **Trading Psychology Note:** "

        if 'revenge_trading' in emotional_state.warning_flags:
            coaching += "Remember, the market will always be there tomorrow. Don't let emotions drive your decisions."

        elif emotional_state.urgency_level > 0.7:
            coaching += "I notice urgency in your message. The best trades often require patience. Rushing leads to mistakes."

        elif emotional_state.primary_emotion == 'greedy':
            coaching += "Greed can be dangerous in trading. Focus on consistent small wins rather than trying to hit home runs."

        elif emotional_state.primary_emotion == 'fearful':
            coaching += "Fear is natural in trading. Start with smaller positions to build confidence and experience."

        else:
            return response  # No coaching needed

        return response + coaching

    async def _apply_corrections(self, response: str, corrections: List[str]) -> str:
        """Apply validation corrections to response"""
        if "Add risk management reminders" in corrections:
            response += "\n\n⚠️ Remember: All trading involves risk. This is for educational purposes using paper trading only."

        return response

    def set_communication_mode(self, mode: CommunicationMode):
        """Set AI communication style"""
        self.communication_mode = mode
        self.logger.info(f"Communication mode set to: {mode.value}")
