"""
A.T.L.A.S Database Manager - Consolidated Database Connection Management
Provides centralized database access with connection pooling and transaction management
"""

import sqlite3
import logging
import threading
from contextlib import contextmanager
from typing import Dict, Any, Optional
from datetime import datetime
import json


class AtlasDatabaseManager:
    """
    Centralized database manager for all A.T.L.A.S database operations
    Prevents connection conflicts and provides consistent access patterns
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Database file paths
        self.db_paths = {
            'memory': 'atlas_memory.db',
            'rag': 'atlas_rag.db',
            'compliance': 'atlas_compliance.db',
            'feedback': 'atlas_feedback.db',
            'enhanced_memory': 'atlas_enhanced_memory.db'
        }
        
        # Connection pools (thread-local storage)
        self._local = threading.local()
        
        # Initialize all databases
        self._initialize_all_databases()
        
        self.logger.info("🗄️ A.T.L.A.S Database Manager initialized")
    
    def _initialize_all_databases(self):
        """Initialize all database schemas"""
        try:
            # Initialize memory database
            self._initialize_memory_db()
            
            # Initialize RAG database
            self._initialize_rag_db()
            
            # Initialize enhanced memory database
            self._initialize_enhanced_memory_db()
            
            # Initialize compliance database
            self._initialize_compliance_db()
            
            # Initialize feedback database
            self._initialize_feedback_db()
            
            self.logger.info("✅ All databases initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing databases: {e}")
    
    @contextmanager
    def get_connection(self, db_type: str):
        """Get database connection with automatic cleanup"""
        if db_type not in self.db_paths:
            raise ValueError(f"Unknown database type: {db_type}")
        
        conn = None
        try:
            conn = sqlite3.connect(self.db_paths[db_type])
            conn.row_factory = sqlite3.Row  # Enable dict-like access
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Database error for {db_type}: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def _initialize_memory_db(self):
        """Initialize memory database schema"""
        with self.get_connection('memory') as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS memory_entries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    memory_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    metadata TEXT,
                    timestamp TEXT NOT NULL,
                    importance_score REAL DEFAULT 0.5
                )
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_session_id ON memory_entries(session_id)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_memory_type ON memory_entries(memory_type)
            ''')
            
            conn.commit()
    
    def _initialize_rag_db(self):
        """Initialize RAG database schema"""
        with self.get_connection('rag') as conn:
            cursor = conn.cursor()
            
            # Book chunks table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS book_chunks (
                    chunk_id TEXT PRIMARY KEY,
                    book_title TEXT,
                    chapter TEXT,
                    section TEXT,
                    content TEXT,
                    metadata TEXT,
                    created_at TEXT
                )
            ''')
            
            # Book metadata table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS book_metadata (
                    book_title TEXT PRIMARY KEY,
                    author TEXT,
                    category TEXT,
                    description TEXT,
                    total_chunks INTEGER,
                    added_at TEXT
                )
            ''')
            
            conn.commit()
    
    def _initialize_enhanced_memory_db(self):
        """Initialize enhanced memory database schema"""
        with self.get_connection('enhanced_memory') as conn:
            cursor = conn.cursor()
            
            # User profiles table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id TEXT PRIMARY KEY,
                    complexity_level TEXT DEFAULT 'beginner',
                    risk_tolerance TEXT DEFAULT 'moderate',
                    communication_style TEXT DEFAULT 'mentor',
                    custom_instructions TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')
            
            # Trading goals table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trading_goals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    goal_type TEXT,
                    target_amount REAL,
                    timeframe TEXT,
                    risk_tolerance TEXT,
                    priority INTEGER,
                    status TEXT DEFAULT 'active',
                    progress REAL DEFAULT 0.0,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')
            
            # Conversation context table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversation_context (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT,
                    current_goal_id INTEGER,
                    conversation_state TEXT DEFAULT 'greeting',
                    last_trade_summary TEXT,
                    user_tone TEXT DEFAULT 'neutral',
                    rejected_signals TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')
            
            # Rejected signals table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rejected_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    signal_type TEXT,
                    symbol TEXT,
                    reason TEXT,
                    rejected_at TEXT
                )
            ''')
            
            # Behavioral patterns table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS behavioral_patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    pattern_type TEXT,
                    severity INTEGER,
                    context TEXT,
                    timestamp TEXT
                )
            ''')
            
            # Session continuity table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS session_continuity (
                    user_id TEXT,
                    session_id TEXT,
                    conversation_state TEXT,
                    last_topic TEXT,
                    context_data TEXT,
                    updated_at TEXT,
                    PRIMARY KEY (user_id, session_id)
                )
            ''')
            
            conn.commit()
    
    def _initialize_compliance_db(self):
        """Initialize compliance database schema"""
        with self.get_connection('compliance') as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS compliance_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    action_type TEXT,
                    details TEXT,
                    timestamp TEXT,
                    compliance_status TEXT
                )
            ''')
            
            conn.commit()
    
    def _initialize_feedback_db(self):
        """Initialize feedback database schema"""
        with self.get_connection('feedback') as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_feedback (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    feedback_type TEXT,
                    content TEXT,
                    rating INTEGER,
                    timestamp TEXT
                )
            ''')
            
            conn.commit()
    
    def store_memory(self, db_type: str, session_id: str, memory_type: str, 
                    content: str, metadata: Dict[str, Any] = None, 
                    importance_score: float = 0.5) -> bool:
        """Store memory entry in specified database"""
        try:
            with self.get_connection(db_type) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO memory_entries
                    (session_id, memory_type, content, metadata, timestamp, importance_score)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    session_id, memory_type, content,
                    json.dumps(metadata or {}),
                    datetime.now().isoformat(),
                    importance_score
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Error storing memory: {e}")
            return False
    
    def get_database_status(self) -> Dict[str, Any]:
        """Get status of all databases"""
        status = {}
        
        for db_type, db_path in self.db_paths.items():
            try:
                with self.get_connection(db_type) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                    
                    status[db_type] = {
                        'path': db_path,
                        'accessible': True,
                        'tables': tables,
                        'table_count': len(tables)
                    }
            except Exception as e:
                status[db_type] = {
                    'path': db_path,
                    'accessible': False,
                    'error': str(e)
                }
        
        return status


# Global database manager instance
db_manager = AtlasDatabaseManager()
