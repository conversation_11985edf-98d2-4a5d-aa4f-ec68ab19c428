"""
A.T.L.A.S Market Engine - Consolidated Market Data and Analysis System
Combines market data, technical analysis, Predicto integration, and intelligence
"""

import asyncio
import aiohttp
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import json
import requests

from config import settings, POPULAR_SYMBOLS
from models import (
    Quote, OHLCV, NewsArticle, SentimentAnalysis, TechnicalIndicators,
    ScanResult, TradingSignal
)

# Import web search service from the original system
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from src.services.web_search_service import WebSearchService
    WEB_SEARCH_AVAILABLE = True
except ImportError:
    WEB_SEARCH_AVAILABLE = False
    WebSearchService = None

# Import the new real-time scanner, market context, proactive assistant, and performance optimizer
from atlas_realtime_scanner import RealTimeTTMScanner, TTMSqueezeSignal, SignalStrength
from atlas_market_context import RealTimeMarketContextEngine, MarketContext
from atlas_proactive_assistant import ProactiveTradingAssistant, ProactiveAlert, AlertType, AlertPriority
from atlas_performance_optimizer import performance_optimizer


class MarketDataService:
    """Real-time market data integration"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.fmp_base_url = settings.FMP_BASE_URL
        self.fmp_api_key = settings.FMP_API_KEY
        self.cache: Dict[str, Any] = {}
        self.cache_ttl = 60  # 1 minute cache
    
    @performance_optimizer.performance_monitor("get_real_time_quote")
    @performance_optimizer.cached_operation(cache_key="quote_{symbol}", cache_type="quote", ttl=60)
    async def get_real_time_quote(self, symbol: str) -> Quote:
        """Get real-time quote for symbol"""

        cache_key = f"quote_{symbol}"
        if self._is_cached(cache_key):
            return Quote(**self.cache[cache_key]['data'])
        
        try:
            url = f"{self.fmp_base_url}/v3/quote/{symbol}"
            params = {"apikey": self.fmp_api_key}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    data = await response.json()
                    
                    if data and len(data) > 0:
                        quote_data = data[0]
                        
                        quote = Quote(
                            symbol=quote_data['symbol'],
                            price=float(quote_data['price']),
                            change=float(quote_data['change']),
                            change_percent=float(quote_data['changesPercentage']),
                            volume=int(quote_data['volume']),
                            avg_volume=int(quote_data.get('avgVolume', 0)),
                            market_cap=float(quote_data.get('marketCap', 0)),
                            pe_ratio=float(quote_data.get('pe', 0)),
                            day_low=float(quote_data['dayLow']),
                            day_high=float(quote_data['dayHigh']),
                            year_low=float(quote_data['yearLow']),
                            year_high=float(quote_data['yearHigh']),
                            bid_price=float(quote_data.get('bid', quote_data['price'])),
                            ask_price=float(quote_data.get('ask', quote_data['price'])),
                            bid_ask_spread=float(quote_data.get('ask', quote_data['price'])) - float(quote_data.get('bid', quote_data['price'])),
                            timestamp=datetime.utcnow()
                        )
                        
                        self._cache_data(cache_key, quote.dict())
                        return quote
                    
        except Exception as e:
            self.logger.error(f"Error fetching quote for {symbol}: {e}")
        
        # Return default quote on error
        return Quote(
            symbol=symbol,
            price=0.0,
            change=0.0,
            change_percent=0.0,
            volume=0,
            avg_volume=0,
            market_cap=0.0,
            pe_ratio=0.0,
            day_low=0.0,
            day_high=0.0,
            year_low=0.0,
            year_high=0.0,
            bid_price=0.0,
            ask_price=0.0,
            bid_ask_spread=0.0,
            timestamp=datetime.utcnow()
        )
    
    async def get_historical_data(self, symbol: str, timeframe: str = "1day", 
                                 limit: int = 100) -> List[OHLCV]:
        """Get historical OHLCV data"""
        
        cache_key = f"historical_{symbol}_{timeframe}_{limit}"
        if self._is_cached(cache_key):
            return [OHLCV(**item) for item in self.cache[cache_key]['data']]
        
        try:
            url = f"{self.fmp_base_url}/v3/historical-price-full/{symbol}"
            params = {
                "apikey": self.fmp_api_key,
                "timeseries": limit
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    data = await response.json()
                    
                    if data and 'historical' in data:
                        ohlcv_data = []
                        for item in data['historical'][:limit]:
                            ohlcv = OHLCV(
                                symbol=symbol,
                                timestamp=datetime.strptime(item['date'], '%Y-%m-%d'),
                                open=float(item['open']),
                                high=float(item['high']),
                                low=float(item['low']),
                                close=float(item['close']),
                                volume=int(item['volume'])
                            )
                            ohlcv_data.append(ohlcv)
                        
                        self._cache_data(cache_key, [item.dict() for item in ohlcv_data])
                        return ohlcv_data
                    
        except Exception as e:
            self.logger.error(f"Error fetching historical data for {symbol}: {e}")
        
        return []
    
    async def get_market_news(self, symbol: Optional[str] = None, limit: int = 10) -> List[NewsArticle]:
        """Get market news"""
        
        try:
            if symbol:
                url = f"{self.fmp_base_url}/v3/stock_news"
                params = {
                    "apikey": self.fmp_api_key,
                    "tickers": symbol,
                    "limit": limit
                }
            else:
                url = f"{self.fmp_base_url}/v3/stock_news"
                params = {
                    "apikey": self.fmp_api_key,
                    "limit": limit
                }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    data = await response.json()
                    
                    news_articles = []
                    for item in data[:limit]:
                        article = NewsArticle(
                            title=item['title'],
                            content=item.get('text', ''),
                            url=item['url'],
                            source=item['site'],
                            published_at=datetime.fromisoformat(item['publishedDate'].replace('Z', '+00:00')),
                            symbols=[item.get('symbol', symbol)] if symbol else []
                        )
                        news_articles.append(article)
                    
                    return news_articles
                    
        except Exception as e:
            self.logger.error(f"Error fetching news: {e}")
            return []
    
    def _is_cached(self, key: str) -> bool:
        """Check if data is cached and still valid"""
        if key not in self.cache:
            return False
        
        cache_time = self.cache[key]['timestamp']
        return (datetime.utcnow() - cache_time).seconds < self.cache_ttl
    
    def _cache_data(self, key: str, data: Any):
        """Cache data with timestamp"""
        self.cache[key] = {
            'data': data,
            'timestamp': datetime.utcnow()
        }


class TechnicalAnalysisEngine:
    """Technical analysis and indicator calculations"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_indicators(self, ohlcv_data: List[OHLCV]) -> TechnicalIndicators:
        """Calculate comprehensive technical indicators"""
        
        if len(ohlcv_data) < 20:
            return self._default_indicators()
        
        # Convert to pandas for easier calculation
        df = pd.DataFrame([
            {
                'close': item.close,
                'high': item.high,
                'low': item.low,
                'volume': item.volume,
                'timestamp': item.timestamp
            }
            for item in ohlcv_data
        ])
        
        df = df.sort_values('timestamp')
        
        try:
            # RSI
            rsi = self._calculate_rsi(df['close'])
            
            # MACD
            macd, macd_signal, macd_histogram = self._calculate_macd(df['close'])
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(df['close'])
            
            # Moving Averages
            sma_20 = df['close'].rolling(20).mean().iloc[-1]
            sma_50 = df['close'].rolling(50).mean().iloc[-1] if len(df) >= 50 else sma_20
            ema_12 = df['close'].ewm(span=12).mean().iloc[-1]
            ema_26 = df['close'].ewm(span=26).mean().iloc[-1]
            
            # ATR
            atr = self._calculate_atr(df)
            
            # Volume indicators
            volume_sma = df['volume'].rolling(20).mean().iloc[-1]
            volume_ratio = df['volume'].iloc[-1] / volume_sma if volume_sma > 0 else 1.0
            
            # TTM Squeeze
            ttm_squeeze = self._calculate_ttm_squeeze(df)
            
            return TechnicalIndicators(
                symbol=ohlcv_data[0].symbol,
                rsi=rsi,
                macd=macd,
                macd_signal=macd_signal,
                macd_histogram=macd_histogram,
                bb_upper=bb_upper,
                bb_middle=bb_middle,
                bb_lower=bb_lower,
                sma_20=sma_20,
                sma_50=sma_50,
                ema_12=ema_12,
                ema_26=ema_26,
                atr=atr,
                volume_sma=volume_sma,
                volume_ratio=volume_ratio,
                ttm_squeeze_signal=ttm_squeeze,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators: {e}")
            return self._default_indicators()
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """Calculate RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return float(rsi.iloc[-1]) if not pd.isna(rsi.iloc[-1]) else 50.0
    
    def _calculate_macd(self, prices: pd.Series) -> Tuple[float, float, float]:
        """Calculate MACD"""
        ema_12 = prices.ewm(span=12).mean()
        ema_26 = prices.ewm(span=26).mean()
        macd = ema_12 - ema_26
        macd_signal = macd.ewm(span=9).mean()
        macd_histogram = macd - macd_signal
        
        return (
            float(macd.iloc[-1]) if not pd.isna(macd.iloc[-1]) else 0.0,
            float(macd_signal.iloc[-1]) if not pd.isna(macd_signal.iloc[-1]) else 0.0,
            float(macd_histogram.iloc[-1]) if not pd.isna(macd_histogram.iloc[-1]) else 0.0
        )
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: int = 2) -> Tuple[float, float, float]:
        """Calculate Bollinger Bands"""
        sma = prices.rolling(period).mean()
        std = prices.rolling(period).std()
        
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        
        return (
            float(upper.iloc[-1]) if not pd.isna(upper.iloc[-1]) else 0.0,
            float(sma.iloc[-1]) if not pd.isna(sma.iloc[-1]) else 0.0,
            float(lower.iloc[-1]) if not pd.isna(lower.iloc[-1]) else 0.0
        )
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        atr = true_range.rolling(period).mean()
        
        return float(atr.iloc[-1]) if not pd.isna(atr.iloc[-1]) else 0.0
    
    def _calculate_ttm_squeeze(self, df: pd.DataFrame) -> str:
        """Calculate TTM Squeeze signal"""
        try:
            # Simplified TTM Squeeze calculation
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(df['close'])
            
            # Keltner Channels (simplified using ATR)
            atr = self._calculate_atr(df)
            kc_upper = bb_middle + (atr * 1.5)
            kc_lower = bb_middle - (atr * 1.5)
            
            # Squeeze condition: Bollinger Bands inside Keltner Channels
            squeeze_on = bb_upper < kc_upper and bb_lower > kc_lower
            
            # Momentum (simplified)
            current_price = df['close'].iloc[-1]
            momentum = current_price - bb_middle
            
            if squeeze_on:
                return "SQUEEZE_ON"
            elif momentum > 0:
                return "SQUEEZE_FIRE_LONG"
            elif momentum < 0:
                return "SQUEEZE_FIRE_SHORT"
            else:
                return "NO_SQUEEZE"
                
        except Exception as e:
            self.logger.error(f"Error calculating TTM Squeeze: {e}")
            return "NO_SQUEEZE"
    
    def _default_indicators(self) -> TechnicalIndicators:
        """Return default indicators when calculation fails"""
        return TechnicalIndicators(
            symbol="UNKNOWN",
            rsi=50.0,
            macd=0.0,
            macd_signal=0.0,
            macd_histogram=0.0,
            bb_upper=0.0,
            bb_middle=0.0,
            bb_lower=0.0,
            sma_20=0.0,
            sma_50=0.0,
            ema_12=0.0,
            ema_26=0.0,
            atr=0.0,
            volume_sma=0.0,
            volume_ratio=1.0,
            ttm_squeeze_signal="NO_SQUEEZE",
            timestamp=datetime.utcnow()
        )


class TechnicalScanner:
    """Technical analysis scanner for opportunities"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.market_data = MarketDataService()
        self.technical_engine = TechnicalAnalysisEngine()
    
    async def scan_ttm_squeeze(self, symbols: List[str] = None) -> List[ScanResult]:
        """Scan for TTM Squeeze opportunities"""
        
        if not symbols:
            symbols = POPULAR_SYMBOLS[:20]  # Scan top 20 symbols
        
        results = []
        
        for symbol in symbols:
            try:
                # Get historical data
                ohlcv_data = await self.market_data.get_historical_data(symbol, limit=50)
                if len(ohlcv_data) < 20:
                    continue
                
                # Calculate indicators
                indicators = self.technical_engine.calculate_indicators(ohlcv_data)
                
                # Get current quote
                quote = await self.market_data.get_real_time_quote(symbol)
                
                # Analyze TTM Squeeze
                signal_strength = self._analyze_ttm_squeeze_strength(indicators, quote)
                
                if signal_strength > 0.6:  # Only include strong signals
                    result = ScanResult(
                        symbol=symbol,
                        signal_type="TTM_SQUEEZE",
                        signal_strength=signal_strength,
                        current_price=quote.price,
                        volume_ratio=indicators.volume_ratio,
                        rsi=indicators.rsi,
                        ttm_squeeze_signal=indicators.ttm_squeeze_signal,
                        reasoning=self._generate_ttm_reasoning(indicators, quote),
                        timestamp=datetime.utcnow()
                    )
                    results.append(result)
                
            except Exception as e:
                self.logger.error(f"Error scanning {symbol}: {e}")
                continue
        
        # Sort by signal strength
        results.sort(key=lambda x: x.signal_strength, reverse=True)
        return results[:10]  # Return top 10
    
    def _analyze_ttm_squeeze_strength(self, indicators: TechnicalIndicators, quote: Quote) -> float:
        """Analyze TTM Squeeze signal strength"""
        
        strength = 0.0
        
        # TTM Squeeze signal
        if indicators.ttm_squeeze_signal == "SQUEEZE_FIRE_LONG":
            strength += 0.4
        elif indicators.ttm_squeeze_signal == "SQUEEZE_FIRE_SHORT":
            strength += 0.3
        elif indicators.ttm_squeeze_signal == "SQUEEZE_ON":
            strength += 0.2
        
        # Volume confirmation
        if indicators.volume_ratio > 1.5:
            strength += 0.2
        elif indicators.volume_ratio > 1.2:
            strength += 0.1
        
        # RSI confirmation
        if 30 < indicators.rsi < 70:  # Not overbought/oversold
            strength += 0.1
        
        # Price action
        if quote.change_percent > 2:  # Strong positive momentum
            strength += 0.2
        elif quote.change_percent > 0:
            strength += 0.1
        
        return min(strength, 1.0)
    
    def _generate_ttm_reasoning(self, indicators: TechnicalIndicators, quote: Quote) -> str:
        """Generate reasoning for TTM Squeeze signal"""
        
        reasoning = f"TTM Squeeze: {indicators.ttm_squeeze_signal.replace('_', ' ').title()}"
        
        if indicators.volume_ratio > 1.5:
            reasoning += f" | High Volume ({indicators.volume_ratio:.1f}x avg)"
        
        if quote.change_percent > 0:
            reasoning += f" | Positive momentum (+{quote.change_percent:.1f}%)"
        
        reasoning += f" | RSI: {indicators.rsi:.0f}"
        
        return reasoning


class PredictoIntegration:
    """Predicto API integration for enhanced predictions"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.api_key = getattr(settings, 'PREDICTO_API_KEY', None)
        self.base_url = 'https://predic.to'
        self.enabled = bool(self.api_key)
        
        if not self.enabled:
            self.logger.warning("Predicto API key not configured - predictions will be simulated")
    
    async def get_forecast(self, symbol: str, days: int = 5) -> Optional[Dict[str, Any]]:
        """Get Predicto forecast for symbol"""
        
        if not self.enabled:
            return self._simulate_forecast(symbol, days)
        
        try:
            url = f"{self.base_url}/forecast"
            headers = {'X-Predicto-Api-Key': self.api_key}
            params = {
                'symbol': symbol,
                'days': days
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data
                    else:
                        self.logger.warning(f"Predicto API error: {response.status}")
                        return self._simulate_forecast(symbol, days)
                        
        except Exception as e:
            self.logger.error(f"Error fetching Predicto forecast: {e}")
            return self._simulate_forecast(symbol, days)
    
    def _simulate_forecast(self, symbol: str, days: int) -> Dict[str, Any]:
        """Simulate forecast when API is not available"""
        
        import random
        
        # Generate simulated forecast
        base_confidence = random.uniform(0.6, 0.8)
        direction = random.choice(['bullish', 'bearish', 'neutral'])
        
        return {
            'symbol': symbol,
            'forecast_days': days,
            'direction': direction,
            'confidence': base_confidence,
            'price_target': None,
            'key_factors': ['Technical momentum', 'Market sentiment', 'Volume analysis'],
            'risk_factors': ['Market volatility', 'Sector rotation'],
            'simulated': True
        }
    
    async def get_market_sentiment(self) -> Dict[str, Any]:
        """Get overall market sentiment"""
        
        if not self.enabled:
            return self._simulate_market_sentiment()
        
        try:
            url = f"{self.base_url}/market-sentiment"
            headers = {'X-Predicto-Api-Key': self.api_key}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data
                    else:
                        return self._simulate_market_sentiment()
                        
        except Exception as e:
            self.logger.error(f"Error fetching market sentiment: {e}")
            return self._simulate_market_sentiment()
    
    def _simulate_market_sentiment(self) -> Dict[str, Any]:
        """Simulate market sentiment"""
        
        import random
        
        sentiment_score = random.uniform(-0.3, 0.3)
        
        if sentiment_score > 0.1:
            sentiment = 'bullish'
        elif sentiment_score < -0.1:
            sentiment = 'bearish'
        else:
            sentiment = 'neutral'
        
        return {
            'overall_sentiment': sentiment,
            'sentiment_score': sentiment_score,
            'confidence': random.uniform(0.6, 0.9),
            'key_drivers': ['Economic data', 'Earnings season', 'Fed policy'],
            'simulated': True
        }

    async def get_enhanced_prediction(self, symbol: str, timeframe: str = "1d",
                                    include_sentiment: bool = True) -> Dict[str, Any]:
        """Get enhanced prediction with sentiment analysis and confidence scoring"""

        try:
            # Get base forecast
            forecast = await self.get_forecast(symbol, 5)

            # Get market sentiment if requested
            sentiment_data = {}
            if include_sentiment:
                sentiment_data = await self.get_market_sentiment()

            # Combine and enhance predictions
            enhanced_prediction = self._combine_predictions(forecast, sentiment_data, symbol)

            return {
                "success": True,
                "symbol": symbol,
                "timeframe": timeframe,
                "prediction": enhanced_prediction,
                "sentiment": sentiment_data,
                "confidence_score": self._calculate_confidence_score(enhanced_prediction),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error getting enhanced prediction: {e}")
            return {
                "success": False,
                "error": str(e),
                "prediction": None
            }

    def _combine_predictions(self, forecast: Dict[str, Any], sentiment: Dict[str, Any],
                           symbol: str) -> Dict[str, Any]:
        """Combine forecast and sentiment into enhanced prediction"""

        if not forecast:
            return {}

        # Start with base forecast
        combined = forecast.copy()

        # Enhance with sentiment if available
        if sentiment:
            sentiment_score = sentiment.get('sentiment_score', 0.0)
            base_confidence = forecast.get('confidence', 0.5)

            # Adjust confidence based on sentiment alignment
            if forecast.get('direction') == 'bullish' and sentiment_score > 0:
                combined['confidence'] = min(base_confidence + (sentiment_score * 0.1), 0.95)
            elif forecast.get('direction') == 'bearish' and sentiment_score < 0:
                combined['confidence'] = min(base_confidence + (abs(sentiment_score) * 0.1), 0.95)
            elif forecast.get('direction') == 'bullish' and sentiment_score < 0:
                combined['confidence'] = max(base_confidence - (abs(sentiment_score) * 0.1), 0.3)
            elif forecast.get('direction') == 'bearish' and sentiment_score > 0:
                combined['confidence'] = max(base_confidence - (sentiment_score * 0.1), 0.3)

            # Add sentiment factors
            sentiment_factors = []
            if abs(sentiment_score) > 0.2:
                sentiment_factors.append(f"Market sentiment: {sentiment.get('overall_sentiment', 'neutral')}")

            combined['key_factors'] = combined.get('key_factors', []) + sentiment_factors
            combined['sentiment_enhanced'] = True

        # Add symbol-specific enhancements
        combined = self._add_symbol_specific_factors(combined, symbol)

        return combined

    def _add_symbol_specific_factors(self, prediction: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """Add symbol-specific prediction factors"""

        enhanced = prediction.copy()

        # Large cap factors
        if symbol in ['AAPL', 'MSFT', 'GOOGL', 'AMZN']:
            enhanced['stability_factor'] = 'high'
            enhanced['liquidity_factor'] = 'excellent'
            if 'key_factors' in enhanced:
                enhanced['key_factors'].append('Large-cap stability')

        # High-beta factors
        elif symbol in ['TSLA', 'NVDA', 'AMD']:
            enhanced['volatility_factor'] = 'high'
            enhanced['beta_factor'] = 'high'
            if 'key_factors' in enhanced:
                enhanced['key_factors'].append('High-beta volatility')

        # ETF factors
        elif symbol in ['SPY', 'QQQ', 'IWM']:
            enhanced['diversification_factor'] = 'high'
            enhanced['market_exposure'] = 'broad'
            if 'key_factors' in enhanced:
                enhanced['key_factors'].append('Broad market exposure')

        return enhanced

    def _calculate_confidence_score(self, prediction: Dict[str, Any]) -> float:
        """Calculate overall confidence score for prediction"""

        if not prediction:
            return 0.0

        base_confidence = prediction.get('confidence', 0.5)

        # Adjust for various factors
        adjustments = 0.0

        # Sentiment enhancement bonus
        if prediction.get('sentiment_enhanced'):
            adjustments += 0.05

        # Stability factor bonus
        if prediction.get('stability_factor') == 'high':
            adjustments += 0.03

        # Volatility factor penalty
        if prediction.get('volatility_factor') == 'high':
            adjustments -= 0.02

        # Liquidity factor bonus
        if prediction.get('liquidity_factor') == 'excellent':
            adjustments += 0.02

        final_confidence = base_confidence + adjustments
        return min(max(final_confidence, 0.1), 0.95)  # Clamp between 10% and 95%

    def _generate_trading_signal_with_context(self, quote: Quote, indicators: TechnicalIndicators,
                                            forecast: Dict[str, Any], market_context: MarketContext) -> str:
        """Generate trading signal with market context awareness"""
        try:
            # Get base signal
            base_signal = self._generate_trading_signal(quote, indicators, forecast)

            if not market_context:
                return base_signal

            # Adjust signal based on market context
            signal_strength = "MODERATE"

            # VIX adjustments
            if market_context.vix_level > 30:
                signal_strength = "WEAK"  # High volatility reduces confidence
            elif market_context.vix_level < 15:
                signal_strength = "STRONG"  # Low volatility increases confidence

            # Market regime adjustments
            if market_context.market_regime == 'crisis':
                if 'BUY' in base_signal:
                    base_signal = base_signal.replace('BUY', 'AVOID')  # Avoid longs in crisis
            elif market_context.market_regime == 'trending':
                if 'HOLD' in base_signal:
                    base_signal = base_signal.replace('HOLD', 'BUY')  # Upgrade holds in trending markets

            # Risk level adjustments
            if market_context.risk_level == 'extreme':
                signal_strength = "WEAK"
            elif market_context.risk_level == 'low':
                signal_strength = "STRONG"

            # Sentiment adjustments
            if abs(market_context.sentiment_score) > 0.4:
                signal_strength = "WEAK"  # Extreme sentiment reduces confidence

            return f"{base_signal} ({signal_strength} - {market_context.market_regime} market)"

        except Exception as e:
            self.logger.error(f"Error generating signal with context: {e}")
            return self._generate_trading_signal(quote, indicators, forecast)


class AtlasMarketEngine:
    """Main market engine coordinating all market functionality"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.market_data = MarketDataService()
        self.technical_engine = TechnicalAnalysisEngine()
        self.scanner = TechnicalScanner()
        self.predicto = PredictoIntegration()

        # Initialize real-time TTM scanner
        self.realtime_scanner = RealTimeTTMScanner()
        self._scanner_task = None

        # Initialize real-time market context engine
        self.market_context_engine = RealTimeMarketContextEngine()

        # Initialize proactive trading assistant (will be initialized later with AI engine)
        self.proactive_assistant = None

        # Initialize web search integration
        self.web_search = WebSearchService() if WEB_SEARCH_AVAILABLE else None
        self.logger.info(f"Web search integration: {'enabled' if self.web_search else 'disabled'}")
        self.logger.info("🎯 A.T.L.A.S Market Engine initialized with real-time scanner and market context")
    
    @performance_optimizer.performance_monitor("get_comprehensive_analysis")
    @performance_optimizer.cached_operation(cache_key="analysis_{symbol}", cache_type="analysis", ttl=300)
    async def get_comprehensive_analysis(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive market analysis for symbol with market context"""

        try:
            # Get all data concurrently including market context
            quote_task = self.market_data.get_real_time_quote(symbol)
            historical_task = self.market_data.get_historical_data(symbol, limit=50)
            news_task = self.market_data.get_market_news(symbol, limit=5)
            forecast_task = self.predicto.get_forecast(symbol)
            context_task = self.market_context_engine.get_current_context()

            quote, historical_data, news, forecast, market_context = await asyncio.gather(
                quote_task, historical_task, news_task, forecast_task, context_task
            )

            # Calculate technical indicators
            indicators = self.technical_engine.calculate_indicators(historical_data)

            # Generate trading signal with market context
            signal = self._generate_trading_signal_with_context(quote, indicators, forecast, market_context)

            # Check for earnings warnings
            earnings_warning = symbol in market_context.earnings_warnings if market_context else False

            return {
                'symbol': symbol,
                'quote': quote.dict(),
                'technical_indicators': indicators.dict(),
                'trading_signal': signal,
                'news': [article.dict() for article in news],
                'forecast': forecast,
                'market_context': market_context.to_dict() if market_context else None,
                'earnings_warning': earnings_warning,
                'context_summary': market_context.get_context_summary() if market_context else None,
                'trading_implications': market_context.get_trading_implications() if market_context else None,
                'analysis_timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive analysis for {symbol}: {e}")
            return {
                'symbol': symbol,
                'error': str(e),
                'analysis_timestamp': datetime.utcnow().isoformat()
            }
    
    async def scan_market_opportunities(self) -> Dict[str, Any]:
        """Scan market for trading opportunities with market context"""

        try:
            # Get market context first
            market_context = await self.market_context_engine.get_current_context()

            # Scan for TTM Squeeze opportunities
            ttm_results = await self.scanner.scan_ttm_squeeze()

            # Get market sentiment (from context or Predicto)
            market_sentiment = market_context.to_dict() if market_context else await self.predicto.get_market_sentiment()

            # Get top movers
            top_movers = await self._get_top_movers()

            return {
                'ttm_squeeze_opportunities': [result.dict() for result in ttm_results],
                'market_sentiment': market_sentiment,
                'market_context': market_context.to_dict() if market_context else None,
                'context_summary': market_context.get_context_summary() if market_context else None,
                'trading_implications': market_context.get_trading_implications() if market_context else None,
                'top_movers': top_movers,
                'scan_timestamp': datetime.utcnow().isoformat(),
                'total_opportunities': len(ttm_results)
            }
            
        except Exception as e:
            self.logger.error(f"Error scanning market opportunities: {e}")
            return {
                'error': str(e),
                'scan_timestamp': datetime.utcnow().isoformat()
            }
    
    def _generate_trading_signal(self, quote: Quote, indicators: TechnicalIndicators, 
                               forecast: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate trading signal based on analysis"""
        
        signal_strength = 0.0
        signal_type = "HOLD"
        reasoning = []
        
        # TTM Squeeze analysis
        if indicators.ttm_squeeze_signal == "SQUEEZE_FIRE_LONG":
            signal_strength += 0.4
            signal_type = "BUY"
            reasoning.append("TTM Squeeze firing long")
        elif indicators.ttm_squeeze_signal == "SQUEEZE_FIRE_SHORT":
            signal_strength += 0.3
            signal_type = "SELL"
            reasoning.append("TTM Squeeze firing short")
        
        # Volume confirmation
        if indicators.volume_ratio > 1.5:
            signal_strength += 0.2
            reasoning.append(f"High volume ({indicators.volume_ratio:.1f}x avg)")
        
        # RSI analysis
        if indicators.rsi < 30:
            signal_strength += 0.2
            reasoning.append("RSI oversold")
        elif indicators.rsi > 70:
            signal_strength -= 0.1
            reasoning.append("RSI overbought")
        
        # Predicto forecast
        if forecast and not forecast.get('simulated', False):
            if forecast.get('direction') == 'bullish' and signal_type == "BUY":
                signal_strength += 0.2
                reasoning.append("Predicto forecast bullish")
            elif forecast.get('direction') == 'bearish' and signal_type == "SELL":
                signal_strength += 0.2
                reasoning.append("Predicto forecast bearish")
        
        # Normalize signal strength
        signal_strength = max(0.0, min(signal_strength, 1.0))
        
        return {
            'signal_type': signal_type,
            'signal_strength': signal_strength,
            'confidence': signal_strength,
            'reasoning': reasoning,
            'entry_price': quote.price,
            'stop_loss': self._calculate_stop_loss(quote.price, indicators.atr),
            'target_price': self._calculate_target_price(quote.price, indicators.atr, signal_type)
        }
    
    def _calculate_stop_loss(self, entry_price: float, atr: float) -> float:
        """Calculate stop loss using ATR"""
        if atr > 0:
            return entry_price - (atr * 1.5)
        else:
            return entry_price * 0.97  # 3% stop loss
    
    def _calculate_target_price(self, entry_price: float, atr: float, signal_type: str) -> float:
        """Calculate target price"""
        if signal_type == "BUY":
            if atr > 0:
                return entry_price + (atr * 2.5)  # 2.5:1 risk/reward
            else:
                return entry_price * 1.06  # 6% target
        else:
            return entry_price  # No target for sell signals in this context
    
    async def _get_top_movers(self) -> List[Dict[str, Any]]:
        """Get top moving stocks"""
        
        try:
            # Get quotes for popular symbols
            symbols = POPULAR_SYMBOLS[:10]
            quotes = []
            
            for symbol in symbols:
                quote = await self.market_data.get_real_time_quote(symbol)
                quotes.append({
                    'symbol': quote.symbol,
                    'price': quote.price,
                    'change_percent': quote.change_percent,
                    'volume_ratio': quote.volume / quote.avg_volume if quote.avg_volume > 0 else 1.0
                })
            
            # Sort by absolute change percent
            quotes.sort(key=lambda x: abs(x['change_percent']), reverse=True)
            return quotes[:5]
            
        except Exception as e:
            self.logger.error(f"Error getting top movers: {e}")
            return []

    async def search_market_context(self, symbol: str, query_type: str = "news") -> Dict[str, Any]:
        """Search for market context using web search integration"""

        if not self.web_search:
            return {
                "success": False,
                "error": "Web search not available. Please configure Google, Bing, or DuckDuckGo API keys.",
                "fallback": "Using internal market data only"
            }

        try:
            # Search query templates
            search_templates = {
                'news': f'{symbol} stock news latest earnings',
                'earnings': f'{symbol} earnings report Q{self._get_current_quarter()} {datetime.now().year}',
                'analyst': f'{symbol} analyst rating price target upgrade downgrade',
                'insider': f'{symbol} insider trading SEC filings',
                'options': f'{symbol} options flow unusual activity',
                'technical': f'{symbol} technical analysis chart pattern breakout'
            }

            query = search_templates.get(query_type, f"{symbol} {query_type}")

            # Perform web search
            search_results = await self.web_search.search(query, num_results=5)

            if search_results.get('success'):
                # Process results for trading context
                processed_results = self._process_market_search_results(
                    search_results['results'], symbol, query_type
                )

                return {
                    "success": True,
                    "symbol": symbol,
                    "query_type": query_type,
                    "results": processed_results,
                    "sentiment_score": self._calculate_search_sentiment(processed_results),
                    "market_impact": self._assess_search_impact(processed_results),
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return search_results

        except Exception as e:
            self.logger.error(f"Error searching market context: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _process_market_search_results(self, results: List[Dict], symbol: str, query_type: str) -> List[Dict]:
        """Process search results for market context"""

        processed = []

        for result in results:
            # Calculate relevance and sentiment
            relevance = self._calculate_market_relevance(result, symbol, query_type)
            sentiment = self._analyze_market_sentiment(result.get('snippet', ''))

            if relevance > 0.3:  # Filter low relevance results
                processed.append({
                    "title": result.get('title', ''),
                    "snippet": result.get('snippet', ''),
                    "url": result.get('url', ''),
                    "source": result.get('source', ''),
                    "relevance": relevance,
                    "sentiment": sentiment,
                    "market_keywords": self._extract_market_keywords(result.get('snippet', ''))
                })

        return sorted(processed, key=lambda x: x['relevance'], reverse=True)

    def _calculate_market_relevance(self, result: Dict, symbol: str, query_type: str) -> float:
        """Calculate market relevance score"""

        title = result.get('title', '').lower()
        snippet = result.get('snippet', '').lower()
        symbol_lower = symbol.lower()

        score = 0.0

        # Symbol relevance
        if symbol_lower in title:
            score += 0.4
        if symbol_lower in snippet:
            score += 0.2

        # Query type relevance
        type_keywords = {
            'earnings': ['earnings', 'revenue', 'profit', 'eps', 'guidance'],
            'analyst': ['analyst', 'rating', 'price target', 'upgrade', 'downgrade'],
            'news': ['stock', 'shares', 'trading', 'market', 'investors'],
            'technical': ['chart', 'pattern', 'breakout', 'support', 'resistance']
        }

        keywords = type_keywords.get(query_type, [])
        for keyword in keywords:
            if keyword in title:
                score += 0.15
            if keyword in snippet:
                score += 0.1

        return min(score, 1.0)

    def _analyze_market_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze market sentiment from text"""

        text_lower = text.lower()

        # Bullish indicators
        bullish_words = [
            'beat', 'exceed', 'strong', 'growth', 'positive', 'bullish', 'upgrade',
            'buy', 'outperform', 'rally', 'surge', 'gain', 'rise', 'up'
        ]

        # Bearish indicators
        bearish_words = [
            'miss', 'weak', 'decline', 'negative', 'bearish', 'downgrade',
            'sell', 'underperform', 'fall', 'drop', 'loss', 'down', 'crash'
        ]

        bullish_count = sum(1 for word in bullish_words if word in text_lower)
        bearish_count = sum(1 for word in bearish_words if word in text_lower)

        if bullish_count > bearish_count:
            sentiment = 'bullish'
            confidence = min(bullish_count / (bullish_count + bearish_count + 1), 0.9)
        elif bearish_count > bullish_count:
            sentiment = 'bearish'
            confidence = min(bearish_count / (bullish_count + bearish_count + 1), 0.9)
        else:
            sentiment = 'neutral'
            confidence = 0.5

        return {
            'sentiment': sentiment,
            'confidence': confidence,
            'bullish_signals': bullish_count,
            'bearish_signals': bearish_count
        }

    def _extract_market_keywords(self, text: str) -> List[str]:
        """Extract important market keywords from text"""

        text_lower = text.lower()

        market_keywords = [
            'earnings', 'revenue', 'profit', 'eps', 'guidance', 'analyst',
            'rating', 'price target', 'upgrade', 'downgrade', 'buy', 'sell',
            'options', 'volume', 'breakout', 'support', 'resistance'
        ]

        found_keywords = [keyword for keyword in market_keywords if keyword in text_lower]
        return found_keywords

    def _calculate_search_sentiment(self, results: List[Dict]) -> float:
        """Calculate overall sentiment from search results"""

        if not results:
            return 0.0

        sentiment_scores = []
        for result in results:
            sentiment_data = result.get('sentiment', {})
            if isinstance(sentiment_data, dict):
                sentiment = sentiment_data.get('sentiment', 'neutral')
                confidence = sentiment_data.get('confidence', 0.5)

                if sentiment == 'bullish':
                    sentiment_scores.append(confidence)
                elif sentiment == 'bearish':
                    sentiment_scores.append(-confidence)
                else:
                    sentiment_scores.append(0.0)

        return sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0.0

    def _assess_search_impact(self, results: List[Dict]) -> str:
        """Assess potential market impact from search results"""

        if not results:
            return 'low'

        high_impact_keywords = ['earnings', 'guidance', 'analyst', 'upgrade', 'downgrade']
        medium_impact_keywords = ['revenue', 'profit', 'rating', 'target']

        high_impact_count = 0
        medium_impact_count = 0

        for result in results:
            keywords = result.get('market_keywords', [])
            high_impact_count += sum(1 for keyword in keywords if keyword in high_impact_keywords)
            medium_impact_count += sum(1 for keyword in keywords if keyword in medium_impact_keywords)

        if high_impact_count >= 2:
            return 'high'
        elif high_impact_count >= 1 or medium_impact_count >= 3:
            return 'medium'
        else:
            return 'low'

    # Real-Time Scanner Integration Methods

    async def start_realtime_scanner(self):
        """Start the real-time TTM Squeeze scanner"""
        try:
            if self._scanner_task and not self._scanner_task.done():
                self.logger.warning("Real-time scanner already running")
                return

            # Initialize scanner
            await self.realtime_scanner.initialize()

            # Start scanning task
            self._scanner_task = asyncio.create_task(self.realtime_scanner.start_scanning())

            self.logger.info("🚀 Real-time TTM Squeeze scanner started")

        except Exception as e:
            self.logger.error(f"Error starting real-time scanner: {e}")
            raise

    def stop_realtime_scanner(self):
        """Stop the real-time TTM Squeeze scanner"""
        try:
            if self._scanner_task and not self._scanner_task.done():
                self.realtime_scanner.stop_scanning()
                self._scanner_task.cancel()
                self.logger.info("🛑 Real-time TTM Squeeze scanner stopped")
            else:
                self.logger.warning("Real-time scanner not running")

        except Exception as e:
            self.logger.error(f"Error stopping real-time scanner: {e}")

    def get_live_ttm_signals(self, min_strength: int = 3) -> List[Dict[str, Any]]:
        """Get current live TTM Squeeze signals"""
        try:
            min_signal_strength = SignalStrength(min_strength)
            signals = self.realtime_scanner.get_signals_by_strength(min_signal_strength)

            return [signal.to_dict() for signal in signals]

        except Exception as e:
            self.logger.error(f"Error getting live TTM signals: {e}")
            return []

    def get_scanner_status(self) -> Dict[str, Any]:
        """Get real-time scanner status"""
        try:
            return self.realtime_scanner.get_scanner_status()
        except Exception as e:
            self.logger.error(f"Error getting scanner status: {e}")
            return {'error': str(e)}

    def register_signal_alert_callback(self, callback: callable):
        """Register callback for TTM signal alerts"""
        try:
            self.realtime_scanner.register_alert_callback(callback)
            self.logger.info("Signal alert callback registered")
        except Exception as e:
            self.logger.error(f"Error registering alert callback: {e}")

    async def cleanup(self):
        """Cleanup market engine resources"""
        try:
            # Stop real-time scanner
            self.stop_realtime_scanner()

            # Wait for scanner task to complete
            if self._scanner_task:
                try:
                    await asyncio.wait_for(self._scanner_task, timeout=5.0)
                except asyncio.TimeoutError:
                    self.logger.warning("Scanner task did not stop gracefully")

            self.logger.info("🧹 Market engine cleanup completed")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

    # Market Context Methods

    async def get_market_context(self, force_refresh: bool = False) -> Dict[str, Any]:
        """Get current market context"""
        try:
            context = await self.market_context_engine.get_current_context(force_refresh)
            return context.to_dict() if context else {}
        except Exception as e:
            self.logger.error(f"Error getting market context: {e}")
            return {}

    def get_market_context_summary(self) -> str:
        """Get human-readable market context summary"""
        try:
            return self.market_context_engine.get_context_summary()
        except Exception as e:
            self.logger.error(f"Error getting context summary: {e}")
            return "Market context not available"

    def get_trading_implications(self) -> str:
        """Get trading implications based on current market context"""
        try:
            return self.market_context_engine.get_trading_implications()
        except Exception as e:
            self.logger.error(f"Error getting trading implications: {e}")
            return "Unable to assess trading implications"

    async def check_earnings_warning(self, symbol: str) -> bool:
        """Check if symbol has earnings this week"""
        try:
            context = await self.market_context_engine.get_current_context()
            return symbol in context.earnings_warnings if context else False
        except Exception as e:
            self.logger.error(f"Error checking earnings warning for {symbol}: {e}")
            return False

    async def get_sector_performance(self) -> Dict[str, float]:
        """Get current sector rotation performance"""
        try:
            context = await self.market_context_engine.get_current_context()
            return context.sector_rotation if context else {}
        except Exception as e:
            self.logger.error(f"Error getting sector performance: {e}")
            return {}

    # Proactive Assistant Methods

    def initialize_proactive_assistant(self, ai_engine):
        """Initialize proactive assistant with AI engine"""
        try:
            self.proactive_assistant = ProactiveTradingAssistant(self, ai_engine)
            self.logger.info("🤖 Proactive assistant initialized")
        except Exception as e:
            self.logger.error(f"Error initializing proactive assistant: {e}")

    async def start_proactive_monitoring(self):
        """Start proactive assistant monitoring"""
        if self.proactive_assistant:
            await self.proactive_assistant.start_monitoring()
        else:
            self.logger.warning("Proactive assistant not initialized")

    def stop_proactive_monitoring(self):
        """Stop proactive assistant monitoring"""
        if self.proactive_assistant:
            self.proactive_assistant.stop_monitoring()

    def get_active_alerts(self, alert_type: AlertType = None) -> List[Dict[str, Any]]:
        """Get active proactive alerts"""
        if self.proactive_assistant:
            alerts = self.proactive_assistant.get_active_alerts(alert_type)
            return [alert.to_dict() for alert in alerts]
        return []

    async def send_custom_alert(self, title: str, message: str,
                              priority: str = 'medium') -> bool:
        """Send custom proactive alert"""
        if self.proactive_assistant:
            priority_map = {
                'low': AlertPriority.LOW,
                'medium': AlertPriority.MEDIUM,
                'high': AlertPriority.HIGH,
                'critical': AlertPriority.CRITICAL
            }
            return await self.proactive_assistant.send_custom_alert(
                title, message, priority_map.get(priority, AlertPriority.MEDIUM)
            )
        return False

    def register_alert_callback(self, callback):
        """Register callback for proactive alerts"""
        if self.proactive_assistant:
            self.proactive_assistant.register_alert_callback(callback)

    def get_proactive_status(self) -> Dict[str, Any]:
        """Get proactive assistant status"""
        if self.proactive_assistant:
            return self.proactive_assistant.get_assistant_status()
        return {'error': 'Proactive assistant not initialized'}

    def _get_current_quarter(self) -> int:
        """Get current quarter"""
        month = datetime.now().month
        return (month - 1) // 3 + 1
