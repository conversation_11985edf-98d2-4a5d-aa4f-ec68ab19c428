"""
A.T.L.A.S ML Price Predictor Module
LSTM neural network for 5-minute bar return predictions with automated execution
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers import <PERSON>
from sklearn.preprocessing import MinMaxScaler
import logging
import asyncio
import pickle
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

from config import settings
from atlas_performance_optimizer import performance_optimizer

# Configure TensorFlow for optimal performance
tf.config.experimental.enable_memory_growth = True
if tf.config.list_physical_devices('GPU'):
    tf.config.experimental.set_memory_growth(tf.config.list_physical_devices('GPU')[0], True)


@dataclass
class PredictionResult:
    """LSTM prediction result"""
    symbol: str
    predicted_return: float
    confidence: float
    signal_strength: str  # 'strong_long', 'weak_long', 'neutral', 'weak_short', 'strong_short'
    features_used: List[str]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'predicted_return': self.predicted_return,
            'confidence': self.confidence,
            'signal_strength': self.signal_strength,
            'features_used': self.features_used,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class ModelMetrics:
    """Model performance metrics"""
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    avg_return: float
    last_updated: datetime


class LSTMPricePredictor:
    """
    Lightweight LSTM neural network for price prediction with automated execution
    """
    
    def __init__(self, market_engine, trading_engine):
        self.logger = logging.getLogger(__name__)
        self.market_engine = market_engine
        self.trading_engine = trading_engine
        
        # Model configuration
        self.sequence_length = 50  # 50-bar lookback window
        self.prediction_threshold_long = 0.001  # 0.1% for long positions
        self.prediction_threshold_short = -0.001  # -0.1% for short positions
        self.confidence_threshold = 0.6  # Minimum confidence for trading
        
        # Model components
        self.model = None
        self.scaler = MinMaxScaler()
        self.feature_columns = [
            'open', 'high', 'low', 'close', 'volume',
            'rsi', 'macd', 'bb_upper', 'bb_lower', 'atr',
            'vix_level', 'spy_return', 'qqq_return'
        ]
        
        # Model paths
        self.model_dir = "models"
        self.model_path = os.path.join(self.model_dir, "lstm_predictor.h5")
        self.scaler_path = os.path.join(self.model_dir, "lstm_scaler.pkl")
        self.metrics_path = os.path.join(self.model_dir, "lstm_metrics.pkl")
        
        # Performance tracking
        self.predictions_made = 0
        self.successful_predictions = 0
        self.last_retrain_date = None
        self.retrain_interval_days = 30
        
        # Error handling
        self.max_retries = 2
        self.fallback_to_ttm = True
        
        # Initialize
        self._ensure_model_directory()
        self._load_or_create_model()
        
        self.logger.info("🧠 LSTM Price Predictor initialized")
    
    def _ensure_model_directory(self):
        """Ensure model directory exists"""
        if not os.path.exists(self.model_dir):
            os.makedirs(self.model_dir)
    
    def _load_or_create_model(self):
        """Load existing model or create new one"""
        try:
            if os.path.exists(self.model_path) and os.path.exists(self.scaler_path):
                self.model = load_model(self.model_path)
                with open(self.scaler_path, 'rb') as f:
                    self.scaler = pickle.load(f)
                self.logger.info("✅ Loaded existing LSTM model")
            else:
                self._create_model()
                self.logger.info("🆕 Created new LSTM model")
        except Exception as e:
            self.logger.error(f"Error loading model: {e}")
            self._create_model()
    
    def _create_model(self):
        """Create new LSTM model"""
        try:
            self.model = Sequential([
                LSTM(32, return_sequences=False, input_shape=(self.sequence_length, len(self.feature_columns))),
                Dropout(0.2),
                Dense(16, activation='relu'),
                Dropout(0.1),
                Dense(1, activation='linear')  # Linear output for return prediction
            ])
            
            self.model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='mse',
                metrics=['mae']
            )
            
            self.logger.info("🏗️ Created new LSTM model architecture")
            
        except Exception as e:
            self.logger.error(f"Error creating model: {e}")
            raise
    
    @performance_optimizer.performance_monitor("lstm_predict")
    async def predict_returns(self, symbol: str, timeframe: str = "5min") -> Optional[PredictionResult]:
        """
        Predict next 5-minute bar returns for symbol
        """
        try:
            # Get historical data with features
            features_df = await self._prepare_features(symbol, timeframe)
            
            if features_df is None or len(features_df) < self.sequence_length:
                self.logger.warning(f"Insufficient data for {symbol}")
                return None
            
            # Prepare sequence for prediction
            sequence = self._prepare_sequence(features_df)
            
            if sequence is None:
                return None
            
            # Make prediction with retry logic
            predicted_return = await self._predict_with_retry(sequence)
            
            if predicted_return is None:
                return None
            
            # Calculate confidence and signal strength
            confidence = self._calculate_confidence(features_df, predicted_return)
            signal_strength = self._determine_signal_strength(predicted_return, confidence)
            
            # Update tracking
            self.predictions_made += 1
            
            result = PredictionResult(
                symbol=symbol,
                predicted_return=predicted_return,
                confidence=confidence,
                signal_strength=signal_strength,
                features_used=self.feature_columns,
                timestamp=datetime.now()
            )
            
            self.logger.info(f"🔮 LSTM Prediction for {symbol}: {predicted_return:.4f} ({signal_strength})")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error predicting returns for {symbol}: {e}")
            return None
    
    async def _prepare_features(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """Prepare feature matrix for prediction"""
        try:
            # Get historical data (need extra bars for technical indicators)
            historical_data = await self.market_engine.market_data.get_historical_data(
                symbol, timeframe=timeframe, limit=200
            )
            
            if not historical_data or len(historical_data) < 100:
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame([{
                'timestamp': bar.timestamp,
                'open': bar.open,
                'high': bar.high,
                'low': bar.low,
                'close': bar.close,
                'volume': bar.volume
            } for bar in historical_data])
            
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            # Calculate technical indicators
            df = self._add_technical_indicators(df)
            
            # Add market context features
            df = await self._add_market_context_features(df)
            
            # Calculate returns for training
            df['future_return'] = df['close'].pct_change().shift(-1)
            
            # Remove NaN values
            df = df.dropna()
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error preparing features: {e}")
            return None
    
    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators to DataFrame"""
        try:
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # MACD
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            df['macd'] = ema_12 - ema_26
            
            # Bollinger Bands
            sma_20 = df['close'].rolling(20).mean()
            std_20 = df['close'].rolling(20).std()
            df['bb_upper'] = sma_20 + (std_20 * 2)
            df['bb_lower'] = sma_20 - (std_20 * 2)
            
            # ATR
            high_low = df['high'] - df['low']
            high_close = np.abs(df['high'] - df['close'].shift())
            low_close = np.abs(df['low'] - df['close'].shift())
            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            df['atr'] = true_range.rolling(14).mean()
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error adding technical indicators: {e}")
            return df
    
    async def _add_market_context_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add market context features"""
        try:
            # Get market context
            market_context = await self.market_engine.get_market_context()
            
            if market_context and 'vix_level' in market_context:
                df['vix_level'] = market_context['vix_level']
            else:
                df['vix_level'] = 20.0  # Default VIX level
            
            # Get SPY and QQQ returns for market context
            spy_data = await self.market_engine.market_data.get_historical_data('SPY', limit=50)
            qqq_data = await self.market_engine.market_data.get_historical_data('QQQ', limit=50)
            
            if spy_data and len(spy_data) > 1:
                spy_return = (spy_data[-1].close - spy_data[-2].close) / spy_data[-2].close
                df['spy_return'] = spy_return
            else:
                df['spy_return'] = 0.0
            
            if qqq_data and len(qqq_data) > 1:
                qqq_return = (qqq_data[-1].close - qqq_data[-2].close) / qqq_data[-2].close
                df['qqq_return'] = qqq_return
            else:
                df['qqq_return'] = 0.0
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error adding market context features: {e}")
            return df
    
    def _prepare_sequence(self, df: pd.DataFrame) -> Optional[np.ndarray]:
        """Prepare sequence for LSTM prediction"""
        try:
            # Select feature columns
            feature_data = df[self.feature_columns].values
            
            # Normalize features
            normalized_data = self.scaler.fit_transform(feature_data)
            
            # Create sequence
            if len(normalized_data) < self.sequence_length:
                return None
            
            sequence = normalized_data[-self.sequence_length:].reshape(1, self.sequence_length, len(self.feature_columns))
            
            return sequence
            
        except Exception as e:
            self.logger.error(f"Error preparing sequence: {e}")
            return None
    
    async def _predict_with_retry(self, sequence: np.ndarray) -> Optional[float]:
        """Make prediction with retry logic for GPU memory errors"""
        for attempt in range(self.max_retries + 1):
            try:
                # Make prediction
                prediction = self.model.predict(sequence, verbose=0)
                return float(prediction[0][0])
                
            except tf.errors.ResourceExhaustedError as e:
                self.logger.warning(f"GPU memory error on attempt {attempt + 1}: {e}")
                if attempt < self.max_retries:
                    # Clear GPU memory and retry
                    tf.keras.backend.clear_session()
                    await asyncio.sleep(1)
                    continue
                else:
                    self.logger.error("Max retries exceeded for GPU memory error")
                    return None
                    
            except Exception as e:
                self.logger.error(f"Prediction error on attempt {attempt + 1}: {e}")
                if attempt < self.max_retries:
                    await asyncio.sleep(0.5)
                    continue
                else:
                    return None
        
        return None
    
    def _calculate_confidence(self, df: pd.DataFrame, predicted_return: float) -> float:
        """Calculate prediction confidence based on market conditions"""
        try:
            # Base confidence from model uncertainty (simplified)
            base_confidence = 0.7
            
            # Adjust based on volatility
            recent_volatility = df['close'].pct_change().tail(10).std()
            if recent_volatility < 0.01:  # Low volatility
                base_confidence += 0.1
            elif recent_volatility > 0.03:  # High volatility
                base_confidence -= 0.1
            
            # Adjust based on prediction magnitude
            if abs(predicted_return) > 0.002:  # Strong prediction
                base_confidence += 0.1
            elif abs(predicted_return) < 0.0005:  # Weak prediction
                base_confidence -= 0.2
            
            return max(0.1, min(0.9, base_confidence))
            
        except Exception as e:
            self.logger.error(f"Error calculating confidence: {e}")
            return 0.5
    
    def _determine_signal_strength(self, predicted_return: float, confidence: float) -> str:
        """Determine signal strength based on prediction and confidence"""
        if confidence < self.confidence_threshold:
            return 'neutral'
        
        if predicted_return > self.prediction_threshold_long:
            if predicted_return > 0.002 and confidence > 0.8:
                return 'strong_long'
            else:
                return 'weak_long'
        elif predicted_return < self.prediction_threshold_short:
            if predicted_return < -0.002 and confidence > 0.8:
                return 'strong_short'
            else:
                return 'weak_short'
        else:
            return 'neutral'

    async def execute_prediction_signal(self, prediction: PredictionResult) -> Optional[Dict[str, Any]]:
        """Execute trading signal based on LSTM prediction"""
        try:
            if prediction.signal_strength == 'neutral':
                return None

            # Determine position size based on confidence and signal strength
            base_position_size = 100  # Base position size

            if prediction.signal_strength in ['strong_long', 'strong_short']:
                position_size = int(base_position_size * 1.5 * prediction.confidence)
            else:
                position_size = int(base_position_size * prediction.confidence)

            # Determine side
            side = 'buy' if 'long' in prediction.signal_strength else 'sell'

            # Execute trade through trading engine
            order_result = await self.trading_engine.place_order(
                symbol=prediction.symbol,
                qty=position_size,
                side=side,
                type='market',
                time_in_force='day',
                source='lstm_predictor'
            )

            if order_result and order_result.get('success'):
                self.successful_predictions += 1
                self.logger.info(f"✅ LSTM trade executed: {side} {position_size} {prediction.symbol}")

                return {
                    'success': True,
                    'order_id': order_result.get('order_id'),
                    'symbol': prediction.symbol,
                    'side': side,
                    'quantity': position_size,
                    'predicted_return': prediction.predicted_return,
                    'confidence': prediction.confidence,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                self.logger.error(f"❌ LSTM trade failed for {prediction.symbol}")
                return None

        except Exception as e:
            self.logger.error(f"Error executing prediction signal: {e}")
            return None

    async def batch_predict_symbols(self, symbols: List[str]) -> List[PredictionResult]:
        """Batch process predictions for multiple symbols"""
        try:
            predictions = []

            # Process in batches to avoid memory issues
            batch_size = 50
            for i in range(0, len(symbols), batch_size):
                batch_symbols = symbols[i:i + batch_size]

                # Create tasks for concurrent processing
                tasks = [self.predict_returns(symbol) for symbol in batch_symbols]
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)

                # Filter successful predictions
                for result in batch_results:
                    if isinstance(result, PredictionResult):
                        predictions.append(result)
                    elif isinstance(result, Exception):
                        self.logger.error(f"Batch prediction error: {result}")

                # Small delay between batches
                await asyncio.sleep(0.1)

            self.logger.info(f"🔮 Batch predictions completed: {len(predictions)} successful")
            return predictions

        except Exception as e:
            self.logger.error(f"Error in batch prediction: {e}")
            return []

    async def train_model(self, symbols: List[str], days_back: int = 730) -> ModelMetrics:
        """Train LSTM model on historical data"""
        try:
            self.logger.info(f"🏋️ Starting LSTM model training on {len(symbols)} symbols...")

            # Collect training data
            all_features = []
            all_targets = []

            for symbol in symbols[:25]:  # Limit to 25 symbols for training
                try:
                    # Get historical data
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=days_back)

                    features_df = await self._prepare_features(symbol, "5min")

                    if features_df is None or len(features_df) < self.sequence_length + 10:
                        continue

                    # Create sequences
                    feature_data = features_df[self.feature_columns].values
                    target_data = features_df['future_return'].values

                    # Normalize features
                    normalized_features = self.scaler.fit_transform(feature_data)

                    # Create sequences
                    for i in range(len(normalized_features) - self.sequence_length):
                        sequence = normalized_features[i:i + self.sequence_length]
                        target = target_data[i + self.sequence_length]

                        if not np.isnan(target):
                            all_features.append(sequence)
                            all_targets.append(target)

                    self.logger.info(f"✅ Processed {symbol}: {len(all_features)} sequences")

                except Exception as e:
                    self.logger.error(f"Error processing {symbol} for training: {e}")
                    continue

            if len(all_features) < 1000:
                self.logger.error("Insufficient training data")
                return None

            # Convert to numpy arrays
            X = np.array(all_features)
            y = np.array(all_targets)

            # Split train/validation
            split_idx = int(len(X) * 0.8)
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]

            self.logger.info(f"📊 Training data: {X_train.shape}, Validation: {X_val.shape}")

            # Train model
            history = self.model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=50,
                batch_size=32,
                verbose=1,
                callbacks=[
                    tf.keras.callbacks.EarlyStopping(patience=10, restore_best_weights=True),
                    tf.keras.callbacks.ReduceLROnPlateau(patience=5, factor=0.5)
                ]
            )

            # Save model and scaler
            self.model.save(self.model_path)
            with open(self.scaler_path, 'wb') as f:
                pickle.dump(self.scaler, f)

            # Calculate metrics
            val_predictions = self.model.predict(X_val)
            metrics = self._calculate_model_metrics(y_val, val_predictions.flatten())

            # Save metrics
            with open(self.metrics_path, 'wb') as f:
                pickle.dump(metrics, f)

            self.last_retrain_date = datetime.now()

            self.logger.info(f"✅ Model training completed. Sharpe: {metrics.sharpe_ratio:.2f}")

            return metrics

        except Exception as e:
            self.logger.error(f"Error training model: {e}")
            return None

    def _calculate_model_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> ModelMetrics:
        """Calculate model performance metrics"""
        try:
            # Convert predictions to trading signals
            signals = np.where(y_pred > self.prediction_threshold_long, 1,
                             np.where(y_pred < self.prediction_threshold_short, -1, 0))

            # Calculate returns
            returns = y_true * signals

            # Calculate metrics
            total_return = np.sum(returns)
            volatility = np.std(returns) * np.sqrt(252 * 24 * 12)  # Annualized for 5-min bars
            sharpe_ratio = (total_return * 252 * 24 * 12) / volatility if volatility > 0 else 0

            # Calculate drawdown
            cumulative_returns = np.cumsum(returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdown)

            # Win rate
            winning_trades = np.sum(returns > 0)
            total_trades = np.sum(signals != 0)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            return ModelMetrics(
                sharpe_ratio=sharpe_ratio,
                max_drawdown=abs(max_drawdown),
                win_rate=win_rate,
                total_trades=total_trades,
                avg_return=np.mean(returns),
                last_updated=datetime.now()
            )

        except Exception as e:
            self.logger.error(f"Error calculating metrics: {e}")
            return ModelMetrics(0, 1, 0, 0, 0, datetime.now())

    async def should_retrain(self) -> bool:
        """Check if model should be retrained"""
        try:
            if self.last_retrain_date is None:
                return True

            days_since_retrain = (datetime.now() - self.last_retrain_date).days

            # Retrain every 30 days or if performance degrades
            if days_since_retrain >= self.retrain_interval_days:
                return True

            # Check recent performance
            if self.predictions_made > 100:
                recent_success_rate = self.successful_predictions / self.predictions_made
                if recent_success_rate < 0.4:  # Below 40% success rate
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking retrain condition: {e}")
            return False

    async def get_model_status(self) -> Dict[str, Any]:
        """Get current model status and metrics"""
        try:
            # Load saved metrics if available
            metrics = None
            if os.path.exists(self.metrics_path):
                with open(self.metrics_path, 'rb') as f:
                    metrics = pickle.load(f)

            return {
                'model_loaded': self.model is not None,
                'predictions_made': self.predictions_made,
                'successful_predictions': self.successful_predictions,
                'success_rate': self.successful_predictions / max(self.predictions_made, 1),
                'last_retrain_date': self.last_retrain_date.isoformat() if self.last_retrain_date else None,
                'should_retrain': await self.should_retrain(),
                'metrics': metrics.__dict__ if metrics else None,
                'feature_columns': self.feature_columns,
                'sequence_length': self.sequence_length,
                'prediction_thresholds': {
                    'long': self.prediction_threshold_long,
                    'short': self.prediction_threshold_short
                }
            }

        except Exception as e:
            self.logger.error(f"Error getting model status: {e}")
            return {'error': str(e)}

    async def fallback_to_ttm_signal(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fallback to TTM Squeeze signals when LSTM fails"""
        try:
            if not self.fallback_to_ttm:
                return None

            # Get TTM signals from market engine
            ttm_signals = self.market_engine.get_live_ttm_signals(min_strength=4)

            # Find signal for this symbol
            for signal in ttm_signals:
                if signal.get('symbol') == symbol:
                    return {
                        'source': 'ttm_fallback',
                        'signal': signal,
                        'reason': 'lstm_prediction_failed'
                    }

            return None

        except Exception as e:
            self.logger.error(f"Error in TTM fallback: {e}")
            return None
