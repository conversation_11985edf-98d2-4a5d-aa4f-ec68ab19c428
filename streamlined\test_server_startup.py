#!/usr/bin/env python3
"""
Test script to diagnose A.T.L.A.S server startup issues
"""

import sys
import os
import traceback
from datetime import datetime

def test_imports():
    """Test all critical imports"""
    print("🔍 Testing imports...")
    
    try:
        print("  - Testing basic imports...")
        import asyncio
        import logging
        from datetime import datetime
        print("  ✅ Basic imports successful")
        
        print("  - Testing FastAPI imports...")
        from fastapi import FastAPI, HTTPException
        from fastapi.middleware.cors import CORSMiddleware
        print("  ✅ FastAPI imports successful")
        
        print("  - Testing config imports...")
        from config import settings
        print(f"  ✅ Config loaded - Environment: {settings.ENVIRONMENT}")
        print(f"  ✅ OpenAI API Key configured: {'Yes' if settings.OPENAI_API_KEY else 'No'}")
        
        print("  - Testing models imports...")
        from models import ChatMessage, AIResponse
        print("  ✅ Models imported successfully")
        
        print("  - Testing orchestrator imports...")
        from atlas_orchestrator import AtlasOrchestrator
        print("  ✅ AtlasOrchestrator imported successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import error: {e}")
        traceback.print_exc()
        return False

def test_orchestrator_init():
    """Test orchestrator initialization"""
    print("\n🔍 Testing orchestrator initialization...")
    
    try:
        from atlas_orchestrator import AtlasOrchestrator
        orchestrator = AtlasOrchestrator()
        print("  ✅ AtlasOrchestrator initialized successfully")
        return orchestrator
        
    except Exception as e:
        print(f"  ❌ Orchestrator initialization error: {e}")
        traceback.print_exc()
        return None

async def test_health_check(orchestrator):
    """Test health check functionality"""
    print("\n🔍 Testing health check...")
    
    try:
        status = await orchestrator.get_system_status()
        print(f"  ✅ Health check successful")
        print(f"  📊 System status: {status.get('system_health', 'unknown')}")
        
        engines = status.get('engines_status', {})
        for engine, status_val in engines.items():
            print(f"    - {engine}: {status_val}")
            
        return True
        
    except Exception as e:
        print(f"  ❌ Health check error: {e}")
        traceback.print_exc()
        return False

def test_chat_processing(orchestrator):
    """Test chat message processing"""
    print("\n🔍 Testing chat processing...")
    
    try:
        import asyncio
        
        async def test_message():
            response = await orchestrator.process_message(
                message="Hello A.T.L.A.S, are you working?",
                session_id="test-session"
            )
            return response
        
        response = asyncio.run(test_message())
        print(f"  ✅ Chat processing successful")
        print(f"  📝 Response type: {response.type}")
        print(f"  📝 Response preview: {response.response[:100]}...")
        return True
        
    except Exception as e:
        print(f"  ❌ Chat processing error: {e}")
        traceback.print_exc()
        return False

def main():
    """Main diagnostic function"""
    print("🚀 A.T.L.A.S Server Startup Diagnostic")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print(f"Python version: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    print()
    
    # Test imports
    if not test_imports():
        print("\n❌ Import test failed - cannot proceed")
        return False
    
    # Test orchestrator initialization
    orchestrator = test_orchestrator_init()
    if not orchestrator:
        print("\n❌ Orchestrator initialization failed - cannot proceed")
        return False
    
    # Test health check
    import asyncio
    health_ok = asyncio.run(test_health_check(orchestrator))
    if not health_ok:
        print("\n⚠️ Health check failed - server may have issues")
    
    # Test chat processing
    chat_ok = test_chat_processing(orchestrator)
    if not chat_ok:
        print("\n⚠️ Chat processing failed - chatbot won't respond")
    
    print("\n" + "=" * 50)
    if health_ok and chat_ok:
        print("✅ All tests passed - server should work correctly")
        return True
    else:
        print("❌ Some tests failed - server has issues")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
