"""
A.T.L.A.S Critical Fixes Validation Test
Tests all critical issues identified in the codebase audit
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_import_structure():
    """Test that all imports work without circular dependencies"""
    print("🔍 Testing import structure...")
    
    try:
        # Test core imports
        from config import settings
        from models import ChatMessage, AIResponse
        print("✅ Core imports successful")
        
        # Test orchestrator import
        from atlas_orchestrator import AtlasOrchestrator
        print("✅ Orchestrator import successful")
        
        # Test server import
        from atlas_server import app
        print("✅ Server import successful")
        
        # Test database manager
        from atlas_database_manager import db_manager
        print("✅ Database manager import successful")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_database_initialization():
    """Test database manager initialization"""
    print("\n🗄️ Testing database initialization...")
    
    try:
        from atlas_database_manager import db_manager
        
        # Test database status
        status = db_manager.get_database_status()
        
        expected_dbs = ['memory', 'rag', 'compliance', 'feedback', 'enhanced_memory']
        for db_type in expected_dbs:
            if db_type in status and status[db_type].get('accessible', False):
                print(f"✅ {db_type} database accessible")
            else:
                print(f"❌ {db_type} database not accessible")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Database initialization error: {e}")
        return False


def test_ml_module_loading():
    """Test ML module lazy loading"""
    print("\n🤖 Testing ML module loading...")
    
    try:
        from atlas_market_engine import _lazy_import_ml_modules, ML_MODULES_AVAILABLE
        
        # Test lazy loading
        ml_loaded = _lazy_import_ml_modules()
        
        if ml_loaded:
            print("✅ ML modules loaded successfully")
        else:
            print("⚠️  ML modules not available (expected if TensorFlow not installed)")
        
        return True
        
    except Exception as e:
        print(f"❌ ML module loading error: {e}")
        return False


def test_orchestrator_initialization():
    """Test orchestrator initialization without errors"""
    print("\n🧠 Testing orchestrator initialization...")
    
    try:
        from atlas_orchestrator import AtlasOrchestrator
        
        # Initialize orchestrator
        orchestrator = AtlasOrchestrator(mentor_mode=True)
        
        # Check that all engines are initialized
        engines = [
            ('ai_engine', orchestrator.ai_engine),
            ('trading_engine', orchestrator.trading_engine),
            ('risk_engine', orchestrator.risk_engine),
            ('market_engine', orchestrator.market_engine),
            ('education_engine', orchestrator.education_engine)
        ]
        
        for engine_name, engine in engines:
            if engine is not None:
                print(f"✅ {engine_name} initialized")
            else:
                print(f"❌ {engine_name} not initialized")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Orchestrator initialization error: {e}")
        return False


def test_education_engine_methods():
    """Test education engine method access"""
    print("\n📚 Testing education engine methods...")
    
    try:
        from atlas_education_engine import AtlasEducationEngine
        
        # Initialize education engine
        education_engine = AtlasEducationEngine()
        
        # Test that add_book_content method exists and is callable
        if hasattr(education_engine, 'add_book_content'):
            print("✅ add_book_content method exists")
            
            # Test method call (should not crash)
            try:
                result = education_engine.add_book_content(
                    "Test Book", "Test Author", "Test content", "test"
                )
                print("✅ add_book_content method callable")
            except Exception as e:
                print(f"⚠️  add_book_content method error (may be expected): {e}")
        else:
            print("❌ add_book_content method missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Education engine error: {e}")
        return False


def test_server_startup():
    """Test server can be imported and configured"""
    print("\n🌐 Testing server startup...")
    
    try:
        from atlas_server import app
        from fastapi.testclient import TestClient
        
        # Create test client
        client = TestClient(app)
        
        # Test health endpoint
        response = client.get("/api/v1/health")
        
        if response.status_code == 200:
            print("✅ Health endpoint accessible")
        else:
            print(f"⚠️  Health endpoint returned {response.status_code}")
        
        return True
        
    except ImportError:
        print("⚠️  TestClient not available, skipping server test")
        return True
    except Exception as e:
        print(f"❌ Server startup error: {e}")
        return False


def test_main_entry_point():
    """Test main.py entry point"""
    print("\n🚀 Testing main entry point...")
    
    try:
        # Test that main.py can be imported
        import main
        
        # Test startup checks function exists
        if hasattr(main, 'startup_checks'):
            print("✅ startup_checks function exists")
        else:
            print("❌ startup_checks function missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Main entry point error: {e}")
        return False


def run_all_tests():
    """Run all critical fix validation tests"""
    print("🧪 A.T.L.A.S Critical Fixes Validation")
    print("=" * 50)
    
    tests = [
        ("Import Structure", test_import_structure),
        ("Database Initialization", test_database_initialization),
        ("ML Module Loading", test_ml_module_loading),
        ("Orchestrator Initialization", test_orchestrator_initialization),
        ("Education Engine Methods", test_education_engine_methods),
        ("Server Startup", test_server_startup),
        ("Main Entry Point", test_main_entry_point)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All critical fixes validated successfully!")
        print("✅ System ready for production use")
        return True
    else:
        print("⚠️  Some tests failed - please review the issues above")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
