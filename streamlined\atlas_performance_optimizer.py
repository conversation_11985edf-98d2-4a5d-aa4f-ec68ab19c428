"""
A.T.L.A.S Performance Optimization Module
Ensures <2 second response times, caching, and system optimization
"""

import asyncio
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
import json
import threading
from functools import wraps
import weakref


@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    operation: str
    start_time: float
    end_time: float
    duration: float
    success: bool
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'operation': self.operation,
            'duration': self.duration,
            'success': self.success,
            'error_message': self.error_message,
            'timestamp': datetime.fromtimestamp(self.end_time).isoformat()
        }


class AdvancedCache:
    """Advanced caching system with TTL and LRU eviction"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.lock = threading.RLock()
        
        # Start cleanup task
        self._cleanup_task = None
        self._start_cleanup()
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache"""
        with self.lock:
            if key not in self.cache:
                return None
            
            item = self.cache[key]
            
            # Check TTL
            if time.time() > item['expires_at']:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                return None
            
            # Update access time
            self.access_times[key] = time.time()
            return item['data']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set item in cache"""
        with self.lock:
            if ttl is None:
                ttl = self.default_ttl
            
            # Evict if at max size
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            expires_at = time.time() + ttl
            self.cache[key] = {
                'data': value,
                'expires_at': expires_at,
                'created_at': time.time()
            }
            self.access_times[key] = time.time()
    
    def delete(self, key: str) -> bool:
        """Delete item from cache"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                return True
            return False
    
    def clear(self) -> None:
        """Clear all cache"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
    
    def _evict_lru(self) -> None:
        """Evict least recently used item"""
        if not self.access_times:
            return
        
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        if lru_key in self.cache:
            del self.cache[lru_key]
        del self.access_times[lru_key]
    
    def _start_cleanup(self):
        """Start background cleanup task"""
        def cleanup_expired():
            while True:
                try:
                    current_time = time.time()
                    with self.lock:
                        expired_keys = [
                            key for key, item in self.cache.items()
                            if current_time > item['expires_at']
                        ]
                        
                        for key in expired_keys:
                            del self.cache[key]
                            if key in self.access_times:
                                del self.access_times[key]
                    
                    time.sleep(60)  # Cleanup every minute
                except Exception as e:
                    logging.error(f"Cache cleanup error: {e}")
                    time.sleep(60)
        
        cleanup_thread = threading.Thread(target=cleanup_expired, daemon=True)
        cleanup_thread.start()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self.lock:
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hit_rate': getattr(self, '_hit_count', 0) / max(getattr(self, '_total_requests', 1), 1),
                'total_requests': getattr(self, '_total_requests', 0)
            }


class PerformanceOptimizer:
    """Main performance optimization system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Performance tracking
        self.metrics: List[PerformanceMetrics] = []
        self.metrics_lock = threading.RLock()
        
        # Caching systems
        self.quote_cache = AdvancedCache(max_size=500, default_ttl=60)  # 1 minute for quotes
        self.analysis_cache = AdvancedCache(max_size=200, default_ttl=300)  # 5 minutes for analysis
        self.news_cache = AdvancedCache(max_size=100, default_ttl=600)  # 10 minutes for news
        self.general_cache = AdvancedCache(max_size=1000, default_ttl=300)  # 5 minutes general
        
        # Performance thresholds
        self.response_time_threshold = 2.0  # 2 seconds
        self.warning_threshold = 1.5  # 1.5 seconds warning
        
        # Optimization settings
        self.enable_caching = True
        self.enable_async_optimization = True
        self.enable_request_batching = True
        
        self.logger.info("🚀 Performance Optimizer initialized")
    
    def performance_monitor(self, operation_name: str):
        """Decorator to monitor performance of operations"""
        def decorator(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                success = True
                error_message = None
                
                try:
                    result = await func(*args, **kwargs)
                    return result
                except Exception as e:
                    success = False
                    error_message = str(e)
                    raise
                finally:
                    end_time = time.time()
                    duration = end_time - start_time
                    
                    # Record metrics
                    metric = PerformanceMetrics(
                        operation=operation_name,
                        start_time=start_time,
                        end_time=end_time,
                        duration=duration,
                        success=success,
                        error_message=error_message
                    )
                    
                    with self.metrics_lock:
                        self.metrics.append(metric)
                        
                        # Keep only last 1000 metrics
                        if len(self.metrics) > 1000:
                            self.metrics = self.metrics[-1000:]
                    
                    # Log performance warnings
                    if duration > self.response_time_threshold:
                        self.logger.warning(f"⚠️ Slow operation: {operation_name} took {duration:.2f}s")
                    elif duration > self.warning_threshold:
                        self.logger.info(f"⏱️ Operation: {operation_name} took {duration:.2f}s")
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                success = True
                error_message = None
                
                try:
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    success = False
                    error_message = str(e)
                    raise
                finally:
                    end_time = time.time()
                    duration = end_time - start_time
                    
                    # Record metrics (same as async)
                    metric = PerformanceMetrics(
                        operation=operation_name,
                        start_time=start_time,
                        end_time=end_time,
                        duration=duration,
                        success=success,
                        error_message=error_message
                    )
                    
                    with self.metrics_lock:
                        self.metrics.append(metric)
                        if len(self.metrics) > 1000:
                            self.metrics = self.metrics[-1000:]
            
            # Return appropriate wrapper based on function type
            if asyncio.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper
        
        return decorator
    
    def cached_operation(self, cache_key: str, cache_type: str = 'general', ttl: Optional[int] = None):
        """Decorator for caching operation results"""
        def decorator(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                if not self.enable_caching:
                    return await func(*args, **kwargs)
                
                # Get appropriate cache
                cache = self._get_cache(cache_type)
                
                # Try to get from cache
                cached_result = cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                result = await func(*args, **kwargs)
                cache.set(cache_key, result, ttl)
                return result
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                if not self.enable_caching:
                    return func(*args, **kwargs)
                
                cache = self._get_cache(cache_type)
                cached_result = cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                result = func(*args, **kwargs)
                cache.set(cache_key, result, ttl)
                return result
            
            if asyncio.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper
        
        return decorator
    
    def _get_cache(self, cache_type: str) -> AdvancedCache:
        """Get cache by type"""
        cache_map = {
            'quote': self.quote_cache,
            'analysis': self.analysis_cache,
            'news': self.news_cache,
            'general': self.general_cache
        }
        return cache_map.get(cache_type, self.general_cache)
    
    def get_performance_summary(self, hours: int = 1) -> Dict[str, Any]:
        """Get performance summary for the last N hours"""
        cutoff_time = time.time() - (hours * 3600)
        
        with self.metrics_lock:
            recent_metrics = [m for m in self.metrics if m.start_time > cutoff_time]
        
        if not recent_metrics:
            return {'error': 'No metrics available'}
        
        # Calculate statistics
        durations = [m.duration for m in recent_metrics]
        successful_ops = [m for m in recent_metrics if m.success]
        failed_ops = [m for m in recent_metrics if not m.success]
        
        avg_duration = sum(durations) / len(durations)
        max_duration = max(durations)
        min_duration = min(durations)
        
        # Performance criteria
        sub_2s_count = sum(1 for d in durations if d < 2.0)
        sub_2s_percentage = (sub_2s_count / len(durations)) * 100
        
        # Error rate
        error_rate = (len(failed_ops) / len(recent_metrics)) * 100
        
        # Operations breakdown
        operation_stats = {}
        for metric in recent_metrics:
            op = metric.operation
            if op not in operation_stats:
                operation_stats[op] = {'count': 0, 'avg_duration': 0, 'errors': 0}
            
            operation_stats[op]['count'] += 1
            operation_stats[op]['avg_duration'] += metric.duration
            if not metric.success:
                operation_stats[op]['errors'] += 1
        
        # Calculate averages
        for op_stats in operation_stats.values():
            op_stats['avg_duration'] /= op_stats['count']
            op_stats['error_rate'] = (op_stats['errors'] / op_stats['count']) * 100
        
        return {
            'time_period_hours': hours,
            'total_operations': len(recent_metrics),
            'successful_operations': len(successful_ops),
            'failed_operations': len(failed_ops),
            'avg_response_time': avg_duration,
            'max_response_time': max_duration,
            'min_response_time': min_duration,
            'sub_2s_percentage': sub_2s_percentage,
            'error_rate': error_rate,
            'operation_breakdown': operation_stats,
            'cache_stats': {
                'quote_cache': self.quote_cache.get_stats(),
                'analysis_cache': self.analysis_cache.get_stats(),
                'news_cache': self.news_cache.get_stats(),
                'general_cache': self.general_cache.get_stats()
            },
            'performance_criteria': {
                'response_time_met': avg_duration < 2.0,
                'error_rate_acceptable': error_rate < 5.0,
                'sub_2s_target_met': sub_2s_percentage >= 95.0
            }
        }
    
    def clear_all_caches(self):
        """Clear all caches"""
        self.quote_cache.clear()
        self.analysis_cache.clear()
        self.news_cache.clear()
        self.general_cache.clear()
        self.logger.info("🧹 All caches cleared")
    
    def optimize_system(self) -> Dict[str, Any]:
        """Run system optimization"""
        optimizations = []
        
        # Analyze recent performance
        summary = self.get_performance_summary(hours=1)
        
        if 'error' not in summary:
            # Check if response times are slow
            if summary['avg_response_time'] > 1.5:
                optimizations.append("Increased cache TTL for frequently accessed data")
                # Increase cache TTL
                self.quote_cache.default_ttl = min(self.quote_cache.default_ttl * 1.2, 300)
                self.analysis_cache.default_ttl = min(self.analysis_cache.default_ttl * 1.2, 600)
            
            # Check error rate
            if summary['error_rate'] > 3.0:
                optimizations.append("Enhanced error handling and retry logic")
            
            # Check cache hit rates
            for cache_name, cache_stats in summary['cache_stats'].items():
                if cache_stats['hit_rate'] < 0.5:
                    optimizations.append(f"Optimized {cache_name} caching strategy")
        
        if not optimizations:
            optimizations.append("System already optimized")
        
        self.logger.info(f"🔧 System optimization completed: {len(optimizations)} improvements")
        
        return {
            'optimizations_applied': optimizations,
            'timestamp': datetime.now().isoformat()
        }


# Global performance optimizer instance
performance_optimizer = PerformanceOptimizer()
