"""
A.T.L.A.S Real-Time Market Context Engine
Integrates news, VIX, sector rotation, earnings warnings, and market sentiment
for comprehensive trading context awareness
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import aiohttp
import pandas as pd
import numpy as np

from config import settings


@dataclass
class MarketContext:
    """Comprehensive market context data"""
    vix_level: float
    vix_percentile: float
    market_sentiment: str  # 'bullish', 'bearish', 'neutral'
    sentiment_score: float  # -1.0 to 1.0
    sector_rotation: Dict[str, float]  # sector performance
    earnings_warnings: List[str]  # symbols with earnings this week
    news_sentiment: Dict[str, float]  # symbol -> sentiment score
    market_regime: str  # 'trending', 'ranging', 'volatile', 'crisis'
    risk_level: str  # 'low', 'moderate', 'high', 'extreme'
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'vix_level': self.vix_level,
            'vix_percentile': self.vix_percentile,
            'market_sentiment': self.market_sentiment,
            'sentiment_score': self.sentiment_score,
            'sector_rotation': self.sector_rotation,
            'earnings_warnings': self.earnings_warnings,
            'news_sentiment': self.news_sentiment,
            'market_regime': self.market_regime,
            'risk_level': self.risk_level,
            'timestamp': self.timestamp.isoformat()
        }


class RealTimeMarketContextEngine:
    """
    Real-time market context engine that continuously monitors:
    - VIX levels and volatility regime
    - Market sentiment from news and social media
    - Sector rotation patterns
    - Earnings calendar warnings
    - Overall market regime detection
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # API configuration
        self.fmp_api_key = settings.FMP_API_KEY
        self.fmp_base_url = "https://financialmodelingprep.com/api"
        
        # Context cache
        self.current_context: Optional[MarketContext] = None
        self.context_cache_ttl = 300  # 5 minutes
        self.last_update = None
        
        # Sector symbols for rotation analysis
        self.sector_etfs = {
            'Technology': 'XLK',
            'Healthcare': 'XLV', 
            'Financials': 'XLF',
            'Consumer Discretionary': 'XLY',
            'Communication Services': 'XLC',
            'Industrials': 'XLI',
            'Consumer Staples': 'XLP',
            'Energy': 'XLE',
            'Utilities': 'XLU',
            'Real Estate': 'XLRE',
            'Materials': 'XLB'
        }
        
        self.logger.info("🌍 Real-Time Market Context Engine initialized")
    
    async def get_current_context(self, force_refresh: bool = False) -> MarketContext:
        """Get current market context (cached or fresh)"""
        try:
            # Check if we need to refresh
            if (force_refresh or 
                not self.current_context or 
                not self.last_update or 
                (datetime.utcnow() - self.last_update).seconds > self.context_cache_ttl):
                
                await self._update_market_context()
            
            return self.current_context
            
        except Exception as e:
            self.logger.error(f"Error getting market context: {e}")
            return self._get_fallback_context()
    
    async def _update_market_context(self):
        """Update market context with fresh data"""
        try:
            self.logger.info("🔄 Updating market context...")
            
            # Gather all context data concurrently
            tasks = [
                self._get_vix_data(),
                self._get_market_sentiment(),
                self._get_sector_rotation(),
                self._get_earnings_warnings(),
                self._get_news_sentiment()
            ]
            
            vix_data, sentiment_data, sector_data, earnings_data, news_data = await asyncio.gather(
                *tasks, return_exceptions=True
            )
            
            # Process results (handle exceptions)
            vix_level, vix_percentile = vix_data if not isinstance(vix_data, Exception) else (20.0, 0.5)
            market_sentiment, sentiment_score = sentiment_data if not isinstance(sentiment_data, Exception) else ('neutral', 0.0)
            sector_rotation = sector_data if not isinstance(sector_data, Exception) else {}
            earnings_warnings = earnings_data if not isinstance(earnings_data, Exception) else []
            news_sentiment = news_data if not isinstance(news_data, Exception) else {}
            
            # Determine market regime and risk level
            market_regime = self._determine_market_regime(vix_level, sentiment_score, sector_rotation)
            risk_level = self._determine_risk_level(vix_level, vix_percentile, sentiment_score)
            
            # Create context object
            self.current_context = MarketContext(
                vix_level=vix_level,
                vix_percentile=vix_percentile,
                market_sentiment=market_sentiment,
                sentiment_score=sentiment_score,
                sector_rotation=sector_rotation,
                earnings_warnings=earnings_warnings,
                news_sentiment=news_sentiment,
                market_regime=market_regime,
                risk_level=risk_level,
                timestamp=datetime.utcnow()
            )
            
            self.last_update = datetime.utcnow()
            self.logger.info(f"✅ Market context updated: {market_regime} regime, {risk_level} risk")
            
        except Exception as e:
            self.logger.error(f"Error updating market context: {e}")
            self.current_context = self._get_fallback_context()
    
    async def _get_vix_data(self) -> tuple[float, float]:
        """Get VIX level and percentile"""
        try:
            # Get VIX data from FMP
            url = f"{self.fmp_base_url}/v3/historical-price-full/^VIX"
            params = {"apikey": self.fmp_api_key, "timeseries": 252}  # 1 year of data
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if data and 'historical' in data and data['historical']:
                            historical = data['historical']
                            current_vix = float(historical[0]['close'])
                            
                            # Calculate percentile over last year
                            vix_values = [float(day['close']) for day in historical[:252]]
                            vix_percentile = sum(1 for v in vix_values if v < current_vix) / len(vix_values)
                            
                            return current_vix, vix_percentile
            
            # Fallback if API fails
            return 20.0, 0.5
            
        except Exception as e:
            self.logger.error(f"Error getting VIX data: {e}")
            return 20.0, 0.5
    
    async def _get_market_sentiment(self) -> tuple[str, float]:
        """Get overall market sentiment"""
        try:
            # Get general market news
            url = f"{self.fmp_base_url}/v3/stock_news"
            params = {"apikey": self.fmp_api_key, "limit": 20}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        news_data = await response.json()
                        
                        # Simple sentiment analysis based on keywords
                        sentiment_score = self._analyze_news_sentiment(news_data)
                        
                        if sentiment_score > 0.1:
                            sentiment = 'bullish'
                        elif sentiment_score < -0.1:
                            sentiment = 'bearish'
                        else:
                            sentiment = 'neutral'
                        
                        return sentiment, sentiment_score
            
            return 'neutral', 0.0
            
        except Exception as e:
            self.logger.error(f"Error getting market sentiment: {e}")
            return 'neutral', 0.0
    
    def _analyze_news_sentiment(self, news_data: List[Dict]) -> float:
        """Simple sentiment analysis of news headlines"""
        try:
            positive_words = ['gain', 'rise', 'up', 'bull', 'strong', 'beat', 'exceed', 'growth', 'positive']
            negative_words = ['fall', 'drop', 'down', 'bear', 'weak', 'miss', 'decline', 'loss', 'negative']
            
            total_score = 0
            total_articles = 0
            
            for article in news_data:
                title = article.get('title', '').lower()
                text = article.get('text', '').lower()
                content = title + ' ' + text
                
                positive_count = sum(1 for word in positive_words if word in content)
                negative_count = sum(1 for word in negative_words if word in content)
                
                if positive_count > 0 or negative_count > 0:
                    article_score = (positive_count - negative_count) / (positive_count + negative_count)
                    total_score += article_score
                    total_articles += 1
            
            return total_score / total_articles if total_articles > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"Error analyzing news sentiment: {e}")
            return 0.0
    
    async def _get_sector_rotation(self) -> Dict[str, float]:
        """Get sector rotation data (performance over last 5 days)"""
        try:
            sector_performance = {}
            
            # Get performance for each sector ETF
            for sector, etf in self.sector_etfs.items():
                try:
                    url = f"{self.fmp_base_url}/v3/historical-price-full/{etf}"
                    params = {"apikey": self.fmp_api_key, "timeseries": 5}
                    
                    async with aiohttp.ClientSession() as session:
                        async with session.get(url, params=params) as response:
                            if response.status == 200:
                                data = await response.json()
                                
                                if data and 'historical' in data and len(data['historical']) >= 2:
                                    current = float(data['historical'][0]['close'])
                                    five_days_ago = float(data['historical'][-1]['close'])
                                    performance = (current - five_days_ago) / five_days_ago * 100
                                    sector_performance[sector] = performance
                    
                    # Small delay to avoid rate limiting
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    self.logger.debug(f"Error getting {sector} performance: {e}")
                    continue
            
            return sector_performance
            
        except Exception as e:
            self.logger.error(f"Error getting sector rotation: {e}")
            return {}
    
    async def _get_earnings_warnings(self) -> List[str]:
        """Get earnings warnings for this week"""
        try:
            # Get earnings calendar for next 7 days
            today = datetime.now()
            next_week = today + timedelta(days=7)
            
            url = f"{self.fmp_base_url}/v3/earning_calendar"
            params = {
                "apikey": self.fmp_api_key,
                "from": today.strftime('%Y-%m-%d'),
                "to": next_week.strftime('%Y-%m-%d')
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        earnings_data = await response.json()
                        
                        # Extract symbols with earnings this week
                        earnings_symbols = []
                        for earning in earnings_data[:50]:  # Limit to top 50
                            symbol = earning.get('symbol')
                            if symbol and symbol not in earnings_symbols:
                                earnings_symbols.append(symbol)
                        
                        return earnings_symbols
            
            return []
            
        except Exception as e:
            self.logger.error(f"Error getting earnings warnings: {e}")
            return []

    async def _get_news_sentiment(self) -> Dict[str, float]:
        """Get news sentiment for major symbols"""
        try:
            major_symbols = ['SPY', 'QQQ', 'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA']
            news_sentiment = {}

            for symbol in major_symbols:
                try:
                    url = f"{self.fmp_base_url}/v3/stock_news"
                    params = {"apikey": self.fmp_api_key, "tickers": symbol, "limit": 5}

                    async with aiohttp.ClientSession() as session:
                        async with session.get(url, params=params) as response:
                            if response.status == 200:
                                news_data = await response.json()
                                sentiment_score = self._analyze_news_sentiment(news_data)
                                news_sentiment[symbol] = sentiment_score

                    await asyncio.sleep(0.1)  # Rate limiting

                except Exception as e:
                    self.logger.debug(f"Error getting news sentiment for {symbol}: {e}")
                    continue

            return news_sentiment

        except Exception as e:
            self.logger.error(f"Error getting news sentiment: {e}")
            return {}

    def _determine_market_regime(self, vix_level: float, sentiment_score: float,
                               sector_rotation: Dict[str, float]) -> str:
        """Determine current market regime"""
        try:
            # VIX-based regime detection
            if vix_level > 30:
                return 'crisis'
            elif vix_level > 25:
                return 'volatile'
            elif vix_level < 15:
                return 'complacent'

            # Sentiment-based adjustments
            if abs(sentiment_score) > 0.3:
                return 'trending'

            # Sector rotation analysis
            if sector_rotation:
                sector_values = list(sector_rotation.values())
                if len(sector_values) > 0:
                    sector_spread = max(sector_values) - min(sector_values)
                    if sector_spread > 5:  # High sector dispersion
                        return 'rotating'

            return 'ranging'

        except Exception as e:
            self.logger.error(f"Error determining market regime: {e}")
            return 'uncertain'

    def _determine_risk_level(self, vix_level: float, vix_percentile: float,
                            sentiment_score: float) -> str:
        """Determine current risk level"""
        try:
            risk_score = 0

            # VIX contribution
            if vix_level > 30:
                risk_score += 3
            elif vix_level > 25:
                risk_score += 2
            elif vix_level > 20:
                risk_score += 1

            # VIX percentile contribution
            if vix_percentile > 0.8:
                risk_score += 2
            elif vix_percentile > 0.6:
                risk_score += 1

            # Sentiment extremes add risk
            if abs(sentiment_score) > 0.4:
                risk_score += 1

            # Determine risk level
            if risk_score >= 5:
                return 'extreme'
            elif risk_score >= 3:
                return 'high'
            elif risk_score >= 1:
                return 'moderate'
            else:
                return 'low'

        except Exception as e:
            self.logger.error(f"Error determining risk level: {e}")
            return 'moderate'

    def _get_fallback_context(self) -> MarketContext:
        """Get fallback context when data is unavailable"""
        return MarketContext(
            vix_level=20.0,
            vix_percentile=0.5,
            market_sentiment='neutral',
            sentiment_score=0.0,
            sector_rotation={},
            earnings_warnings=[],
            news_sentiment={},
            market_regime='uncertain',
            risk_level='moderate',
            timestamp=datetime.utcnow()
        )

    def get_context_summary(self) -> str:
        """Get human-readable context summary"""
        if not self.current_context:
            return "Market context not available"

        ctx = self.current_context

        summary = f"📊 **Market Context Summary:**\n"
        summary += f"• **VIX Level:** {ctx.vix_level:.1f} ({ctx.vix_percentile*100:.0f}th percentile)\n"
        summary += f"• **Market Sentiment:** {ctx.market_sentiment.title()} ({ctx.sentiment_score:+.2f})\n"
        summary += f"• **Market Regime:** {ctx.market_regime.title()}\n"
        summary += f"• **Risk Level:** {ctx.risk_level.title()}\n"

        if ctx.earnings_warnings:
            summary += f"• **Earnings This Week:** {len(ctx.earnings_warnings)} companies\n"

        if ctx.sector_rotation:
            top_sector = max(ctx.sector_rotation.items(), key=lambda x: x[1])
            bottom_sector = min(ctx.sector_rotation.items(), key=lambda x: x[1])
            summary += f"• **Sector Leaders:** {top_sector[0]} (+{top_sector[1]:.1f}%)\n"
            summary += f"• **Sector Laggards:** {bottom_sector[0]} ({bottom_sector[1]:+.1f}%)\n"

        return summary

    def get_trading_implications(self) -> str:
        """Get trading implications based on current context"""
        if not self.current_context:
            return "Unable to assess trading implications"

        ctx = self.current_context
        implications = []

        # VIX implications
        if ctx.vix_level > 25:
            implications.append("High volatility - consider smaller position sizes")
        elif ctx.vix_level < 15:
            implications.append("Low volatility - good for momentum strategies")

        # Sentiment implications
        if ctx.sentiment_score > 0.3:
            implications.append("Strong bullish sentiment - watch for overextension")
        elif ctx.sentiment_score < -0.3:
            implications.append("Strong bearish sentiment - look for oversold bounces")

        # Regime implications
        if ctx.market_regime == 'crisis':
            implications.append("Crisis mode - focus on defensive positions")
        elif ctx.market_regime == 'trending':
            implications.append("Trending market - momentum strategies favored")
        elif ctx.market_regime == 'ranging':
            implications.append("Range-bound market - mean reversion strategies")

        # Risk implications
        if ctx.risk_level == 'extreme':
            implications.append("Extreme risk - consider cash or hedging")
        elif ctx.risk_level == 'high':
            implications.append("High risk environment - reduce position sizes")

        if not implications:
            implications.append("Balanced market conditions - standard strategies apply")

        return "🎯 **Trading Implications:**\n" + "\n".join(f"• {imp}" for imp in implications)
