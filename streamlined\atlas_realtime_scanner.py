"""
A.T.L.A.S Real-Time TTM Squeeze Scanner
Implements continuous monitoring of S&P 500 + $100B+ market cap stocks
for the specific 5-bar TTM Squeeze pattern with real-time alerts
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from enum import Enum
import pandas as pd
import numpy as np
import aiohttp
import json

from config import settings


class SignalStrength(Enum):
    """Signal strength ratings (1-5 stars)"""
    ONE_STAR = 1
    TWO_STAR = 2
    THREE_STAR = 3
    FOUR_STAR = 4
    FIVE_STAR = 5


@dataclass
class TTMSqueezeSignal:
    """Real-time TTM Squeeze signal data"""
    symbol: str
    current_price: float
    signal_strength: SignalStrength
    direction: str  # 'long' or 'short'
    timestamp: datetime
    histogram_value: float
    momentum_value: float
    ema5_rising: bool
    down_bar_count: int
    volume_ratio: float
    reasoning: str
    confidence_score: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['signal_strength'] = self.signal_strength.value
        return data


class RealTimeTTMScanner:
    """
    Real-time TTM Squeeze scanner implementing the 5-bar pattern:
    - 5+ consecutive down bars in TTM Squeeze histogram
    - Followed by uptick with rising momentum
    - EMA5 rising confirmation
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Scanner configuration
        self.scan_interval = 90  # 1.5 minutes between scans
        self.timeframe = "5min"
        self.min_signal_strength = SignalStrength.THREE_STAR
        
        # TTM Squeeze parameters
        self.bb_period = 20
        self.bb_stddev = 2.0
        self.kc_period = 20
        self.kc_multiplier = 1.5
        self.momentum_lookback = 12
        
        # Pattern detection parameters
        self.min_down_bars = 5
        self.histogram_lookback = 20
        
        # Universe of symbols to scan
        self.scan_universe: Set[str] = set()
        self.active_signals: Dict[str, TTMSqueezeSignal] = {}
        
        # API configuration
        self.fmp_api_key = settings.FMP_API_KEY
        self.fmp_base_url = "https://financialmodelingprep.com/api"
        
        # Scanner state
        self.is_running = False
        self.last_scan_time = None
        
        # Alert callbacks
        self.alert_callbacks: List[callable] = []
        
        self.logger.info("🎯 Real-Time TTM Squeeze Scanner initialized")
    
    async def initialize(self):
        """Initialize scanner with symbol universe"""
        try:
            # Build scan universe: S&P 500 + $100B+ market cap stocks
            await self._build_scan_universe()
            
            self.logger.info(f"📊 Scanner initialized with {len(self.scan_universe)} symbols")
            
        except Exception as e:
            self.logger.error(f"Error initializing scanner: {e}")
            raise
    
    async def _build_scan_universe(self):
        """Build universe of symbols to scan"""
        try:
            # Start with core high-quality symbols
            core_symbols = {
                # Mega-cap tech
                'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'TSLA', 'NVDA', 'NFLX',
                # Finance
                'JPM', 'BAC', 'WFC', 'GS', 'MS', 'BRK.B', 'V', 'MA',
                # Healthcare
                'JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO', 'ABT', 'LLY',
                # Consumer
                'WMT', 'HD', 'PG', 'KO', 'PEP', 'MCD', 'NKE', 'COST',
                # Industrial
                'BA', 'CAT', 'GE', 'MMM', 'HON', 'UPS', 'LMT', 'RTX',
                # Energy
                'XOM', 'CVX', 'COP', 'EOG', 'SLB',
                # Key ETFs
                'SPY', 'QQQ', 'IWM', 'DIA', 'VTI'
            }
            
            self.scan_universe.update(core_symbols)
            
            # Add additional symbols from screening (market cap >= $100B)
            await self._add_large_cap_symbols()
            
        except Exception as e:
            self.logger.error(f"Error building scan universe: {e}")
    
    async def _add_large_cap_symbols(self):
        """Add large-cap symbols from screening"""
        try:
            url = f"{self.fmp_base_url}/v3/stock-screener"
            params = {
                "apikey": self.fmp_api_key,
                "marketCapMoreThan": 100000000000,  # $100B+
                "volumeMoreThan": 1000000,  # 1M+ volume
                "priceMoreThan": 10,
                "isActivelyTrading": True,
                "limit": 100
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        for stock in data[:50]:  # Top 50 additional symbols
                            symbol = stock.get('symbol')
                            if symbol and '.' not in symbol:  # Avoid complex symbols
                                self.scan_universe.add(symbol)
                                
        except Exception as e:
            self.logger.warning(f"Could not add screened symbols: {e}")
    
    async def start_scanning(self):
        """Start continuous real-time scanning"""
        if self.is_running:
            self.logger.warning("Scanner already running")
            return
        
        self.is_running = True
        self.logger.info("🚀 Starting real-time TTM Squeeze scanning...")
        
        try:
            while self.is_running:
                scan_start = datetime.utcnow()
                
                # Perform scan
                new_signals = await self._perform_scan()
                
                # Process new signals
                await self._process_new_signals(new_signals)
                
                # Update scan time
                self.last_scan_time = scan_start
                
                # Calculate next scan time
                scan_duration = (datetime.utcnow() - scan_start).total_seconds()
                sleep_time = max(self.scan_interval - scan_duration, 10)  # Min 10 seconds
                
                self.logger.info(f"📊 Scan completed in {scan_duration:.1f}s, sleeping {sleep_time:.1f}s")
                
                # Sleep until next scan
                await asyncio.sleep(sleep_time)
                
        except Exception as e:
            self.logger.error(f"Error in scanning loop: {e}")
        finally:
            self.is_running = False
    
    def stop_scanning(self):
        """Stop continuous scanning"""
        self.is_running = False
        self.logger.info("🛑 Stopping real-time scanner")
    
    async def _perform_scan(self) -> List[TTMSqueezeSignal]:
        """Perform single scan of all symbols"""
        new_signals = []
        
        # Process symbols in batches to avoid API limits
        batch_size = 10
        symbol_list = list(self.scan_universe)
        
        for i in range(0, len(symbol_list), batch_size):
            batch = symbol_list[i:i + batch_size]
            
            # Process batch concurrently
            tasks = [self._scan_symbol(symbol) for symbol in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in batch_results:
                if isinstance(result, TTMSqueezeSignal):
                    new_signals.append(result)
                elif isinstance(result, Exception):
                    self.logger.debug(f"Scan error: {result}")
            
            # Small delay between batches
            await asyncio.sleep(0.2)
        
        return new_signals
    
    async def _scan_symbol(self, symbol: str) -> Optional[TTMSqueezeSignal]:
        """Scan single symbol for TTM Squeeze 5-bar pattern"""
        try:
            # Fetch intraday data
            df = await self._fetch_intraday_data(symbol)
            if df.empty or len(df) < 30:
                return None
            
            # Calculate TTM indicators
            df = self._calculate_ttm_indicators(df)
            
            # Detect 5-bar pattern
            signal = self._detect_5bar_pattern(df, symbol)
            
            return signal
            
        except Exception as e:
            self.logger.debug(f"Error scanning {symbol}: {e}")
            return None
    
    async def _fetch_intraday_data(self, symbol: str, limit: int = 100) -> pd.DataFrame:
        """Fetch intraday data for symbol"""
        try:
            url = f"{self.fmp_base_url}/v3/historical-chart/{self.timeframe}/{symbol}"
            params = {"apikey": self.fmp_api_key, "limit": limit}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status != 200:
                        return pd.DataFrame()
                    
                    data = await response.json()
                    
                    if not data:
                        return pd.DataFrame()
                    
                    # Convert to DataFrame
                    df = pd.DataFrame(data)
                    
                    # Ensure proper column names and types
                    if 'date' in df.columns:
                        df['datetime'] = pd.to_datetime(df['date'])
                        df = df.sort_values('datetime').reset_index(drop=True)
                        
                        # Ensure numeric columns
                        for col in ['open', 'high', 'low', 'close', 'volume']:
                            if col in df.columns:
                                df[col] = pd.to_numeric(df[col], errors='coerce')
                    
                    return df
                    
        except Exception as e:
            self.logger.debug(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()

    def _calculate_ttm_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate TTM Squeeze indicators"""
        try:
            if df.empty or len(df) < self.bb_period:
                return df

            # Bollinger Bands
            df['bb_middle'] = df['close'].rolling(window=self.bb_period).mean()
            bb_std = df['close'].rolling(window=self.bb_period).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * self.bb_stddev)
            df['bb_lower'] = df['bb_middle'] - (bb_std * self.bb_stddev)

            # ATR for Keltner Channels
            df['tr'] = np.maximum(
                df['high'] - df['low'],
                np.maximum(
                    abs(df['high'] - df['close'].shift(1)),
                    abs(df['low'] - df['close'].shift(1))
                )
            )
            df['atr'] = df['tr'].rolling(window=self.kc_period).mean()

            # Keltner Channels
            df['kc_middle'] = df['close'].rolling(window=self.kc_period).mean()
            df['kc_upper'] = df['kc_middle'] + (self.kc_multiplier * df['atr'])
            df['kc_lower'] = df['kc_middle'] - (self.kc_multiplier * df['atr'])

            # TTM Squeeze condition
            df['squeeze_on'] = (df['bb_lower'] > df['kc_lower']) & (df['bb_upper'] < df['kc_upper'])

            # TTM Histogram (momentum oscillator using linear regression slope)
            df['ttm_histogram'] = self._calculate_histogram(df)

            # Enhanced momentum indicators
            df['ema5'] = df['close'].ewm(span=5).mean()
            df['momentum'] = df['close'] - df['close'].shift(self.momentum_lookback)

            # Volume analysis
            df['volume_sma'] = df['volume'].rolling(window=20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']

            return df

        except Exception as e:
            self.logger.error(f"Error calculating TTM indicators: {e}")
            return df

    def _calculate_histogram(self, df: pd.DataFrame) -> pd.Series:
        """Calculate TTM Histogram using linear regression slope"""
        def linear_regression_slope(series):
            if len(series) < 2:
                return 0
            x = np.arange(len(series))
            y = series.values
            try:
                slope = np.polyfit(x, y, 1)[0]
                return slope * 1000  # Scale for visibility
            except:
                return 0

        return df['close'].rolling(window=self.histogram_lookback).apply(
            linear_regression_slope, raw=False
        )

    def _detect_5bar_pattern(self, df: pd.DataFrame, symbol: str) -> Optional[TTMSqueezeSignal]:
        """
        Detect the specific 5-bar TTM Squeeze pattern:
        1. 5+ consecutive down bars in histogram
        2. Current bar shows uptick in histogram
        3. Rising momentum confirmation
        4. EMA5 rising vs 1 bar ago
        """
        try:
            if len(df) < 10:
                return None

            current = df.iloc[-1]
            recent = df.iloc[-10:]  # Last 10 bars for analysis

            # Get histogram values
            hist_values = recent['ttm_histogram'].dropna()
            if len(hist_values) < 6:
                return None

            # Check for 5+ consecutive down bars
            down_count = self._count_consecutive_down_bars(hist_values)
            if down_count < self.min_down_bars:
                return None

            # Check for current uptick
            current_hist = hist_values.iloc[-1]
            prev_hist = hist_values.iloc[-2]

            if current_hist <= prev_hist:
                return None  # No uptick

            # Check momentum rising
            current_momentum = current.get('momentum', 0)
            prev_momentum = df.iloc[-2].get('momentum', 0)

            if current_momentum <= prev_momentum:
                return None  # Momentum not rising

            # Check EMA5 rising
            current_ema5 = current.get('ema5', 0)
            prev_ema5 = df.iloc[-2].get('ema5', 0)

            ema5_rising = current_ema5 > prev_ema5

            # Calculate signal strength
            signal_strength = self._calculate_signal_strength(
                down_count, current_hist, current_momentum, ema5_rising, current
            )

            # Only return signals with minimum strength
            if signal_strength.value < self.min_signal_strength.value:
                return None

            # Determine direction
            direction = 'long' if current_hist > 0 and current_momentum > 0 else 'short'

            # Generate reasoning
            reasoning = self._generate_reasoning(
                down_count, current_hist, current_momentum, ema5_rising, direction
            )

            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(
                down_count, current_hist, current_momentum, ema5_rising, current
            )

            return TTMSqueezeSignal(
                symbol=symbol,
                current_price=float(current['close']),
                signal_strength=signal_strength,
                direction=direction,
                timestamp=datetime.utcnow(),
                histogram_value=float(current_hist),
                momentum_value=float(current_momentum),
                ema5_rising=ema5_rising,
                down_bar_count=down_count,
                volume_ratio=float(current.get('volume_ratio', 1.0)),
                reasoning=reasoning,
                confidence_score=confidence_score
            )

        except Exception as e:
            self.logger.error(f"Error detecting 5-bar pattern for {symbol}: {e}")
            return None

    def _count_consecutive_down_bars(self, hist_values: pd.Series) -> int:
        """Count consecutive down bars in histogram"""
        try:
            count = 0
            for i in range(len(hist_values) - 2, 0, -1):  # Start from second-to-last
                if hist_values.iloc[i] < hist_values.iloc[i-1]:
                    count += 1
                else:
                    break
            return count

        except Exception as e:
            self.logger.error(f"Error counting down bars: {e}")
            return 0

    def _calculate_signal_strength(self, down_count: int, hist_value: float,
                                 momentum: float, ema5_rising: bool,
                                 current: pd.Series) -> SignalStrength:
        """Calculate signal strength (1-5 stars)"""
        try:
            score = 0

            # Down bar count contribution (0-2 points)
            if down_count >= 7:
                score += 2
            elif down_count >= 5:
                score += 1.5
            else:
                score += 1

            # Histogram strength (0-1 points)
            if abs(hist_value) > 0.5:
                score += 1
            elif abs(hist_value) > 0.2:
                score += 0.5

            # Momentum strength (0-1 points)
            if momentum > 1.0:
                score += 1
            elif momentum > 0.5:
                score += 0.5

            # EMA5 rising (0-0.5 points)
            if ema5_rising:
                score += 0.5

            # Volume confirmation (0-0.5 points)
            volume_ratio = current.get('volume_ratio', 1.0)
            if volume_ratio > 1.5:
                score += 0.5
            elif volume_ratio > 1.2:
                score += 0.25

            # Convert to star rating
            if score >= 4.5:
                return SignalStrength.FIVE_STAR
            elif score >= 3.5:
                return SignalStrength.FOUR_STAR
            elif score >= 2.5:
                return SignalStrength.THREE_STAR
            elif score >= 1.5:
                return SignalStrength.TWO_STAR
            else:
                return SignalStrength.ONE_STAR

        except Exception as e:
            self.logger.error(f"Error calculating signal strength: {e}")
            return SignalStrength.ONE_STAR

    def _calculate_confidence_score(self, down_count: int, hist_value: float,
                                  momentum: float, ema5_rising: bool,
                                  current: pd.Series) -> float:
        """Calculate confidence score (0.0 to 1.0)"""
        try:
            confidence = 0.0

            # Base confidence from pattern strength
            confidence += min(down_count / 10.0, 0.4)  # Max 0.4 for down bars
            confidence += min(abs(hist_value) / 2.0, 0.2)  # Max 0.2 for histogram
            confidence += min(momentum / 5.0, 0.2)  # Max 0.2 for momentum

            # Confirmations
            if ema5_rising:
                confidence += 0.1

            volume_ratio = current.get('volume_ratio', 1.0)
            if volume_ratio > 1.5:
                confidence += 0.1

            return min(confidence, 1.0)

        except Exception as e:
            self.logger.error(f"Error calculating confidence: {e}")
            return 0.5

    def _generate_reasoning(self, down_count: int, hist_value: float,
                          momentum: float, ema5_rising: bool, direction: str) -> str:
        """Generate human-readable reasoning for the signal"""
        try:
            reasoning_parts = []

            # Pattern description
            reasoning_parts.append(f"TTM Squeeze fired after {down_count} consecutive down bars")

            # Histogram analysis
            if hist_value > 0:
                reasoning_parts.append("Histogram turned positive (bullish momentum)")
            else:
                reasoning_parts.append("Histogram uptick from negative territory")

            # Momentum confirmation
            if momentum > 1.0:
                reasoning_parts.append("Strong momentum confirmation")
            elif momentum > 0:
                reasoning_parts.append("Momentum turning positive")

            # EMA confirmation
            if ema5_rising:
                reasoning_parts.append("EMA5 rising (trend confirmation)")

            # Direction
            reasoning_parts.append(f"Signal direction: {direction.upper()}")

            return " | ".join(reasoning_parts)

        except Exception as e:
            self.logger.error(f"Error generating reasoning: {e}")
            return f"TTM Squeeze {direction} signal detected"

    async def _process_new_signals(self, new_signals: List[TTMSqueezeSignal]):
        """Process and alert on new signals"""
        try:
            high_priority_signals = []

            for signal in new_signals:
                # Update active signals
                self.active_signals[signal.symbol] = signal

                # Check if this is a high-priority alert (4-5 stars)
                if signal.signal_strength.value >= SignalStrength.FOUR_STAR.value:
                    high_priority_signals.append(signal)

            # Send alerts for high-priority signals
            if high_priority_signals:
                await self._send_alerts(high_priority_signals)

            # Log scan results
            if new_signals:
                self.logger.info(f"🎯 Found {len(new_signals)} TTM signals, "
                               f"{len(high_priority_signals)} high-priority")

        except Exception as e:
            self.logger.error(f"Error processing new signals: {e}")

    async def _send_alerts(self, signals: List[TTMSqueezeSignal]):
        """Send alerts for high-priority signals"""
        try:
            for signal in signals:
                alert_message = (
                    f"🚨 {signal.symbol} TTM SQUEEZE ALERT! "
                    f"{'⭐' * signal.signal_strength.value} "
                    f"${signal.current_price:.2f} {signal.direction.upper()}"
                )

                self.logger.info(alert_message)

                # Call registered alert callbacks
                for callback in self.alert_callbacks:
                    try:
                        await callback(signal)
                    except Exception as e:
                        self.logger.error(f"Error in alert callback: {e}")

        except Exception as e:
            self.logger.error(f"Error sending alerts: {e}")

    def register_alert_callback(self, callback: callable):
        """Register callback for signal alerts"""
        self.alert_callbacks.append(callback)

    def get_active_signals(self) -> List[TTMSqueezeSignal]:
        """Get current active signals"""
        return list(self.active_signals.values())

    def get_signals_by_strength(self, min_strength: SignalStrength) -> List[TTMSqueezeSignal]:
        """Get signals filtered by minimum strength"""
        return [
            signal for signal in self.active_signals.values()
            if signal.signal_strength.value >= min_strength.value
        ]

    def clear_old_signals(self, max_age_minutes: int = 30):
        """Clear signals older than specified age"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(minutes=max_age_minutes)

            old_symbols = [
                symbol for symbol, signal in self.active_signals.items()
                if signal.timestamp < cutoff_time
            ]

            for symbol in old_symbols:
                del self.active_signals[symbol]

            if old_symbols:
                self.logger.info(f"🧹 Cleared {len(old_symbols)} old signals")

        except Exception as e:
            self.logger.error(f"Error clearing old signals: {e}")

    def get_scanner_status(self) -> Dict[str, Any]:
        """Get current scanner status"""
        return {
            'is_running': self.is_running,
            'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None,
            'scan_universe_size': len(self.scan_universe),
            'active_signals_count': len(self.active_signals),
            'high_priority_signals': len(self.get_signals_by_strength(SignalStrength.FOUR_STAR)),
            'scan_interval_seconds': self.scan_interval,
            'min_signal_strength': self.min_signal_strength.value
        }
