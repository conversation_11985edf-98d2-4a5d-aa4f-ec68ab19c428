import requests
import time
import json

def test_server_connection():
    """Test if the server is responding"""
    
    print("Testing server connection...")
    
    # Test basic connection
    try:
        response = requests.get("http://localhost:8080/api/v1/health", timeout=10)
        print(f"Health check status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Health response: {json.dumps(data, indent=2)}")
        else:
            print(f"Health check failed: {response.text}")
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server - server is not running")
        return False
    except requests.exceptions.Timeout:
        print("❌ Server timeout - server is not responding")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    # Test chat endpoint
    try:
        chat_data = {
            "message": "Hello A.T.L.A.S",
            "session_id": "test-session"
        }
        response = requests.post("http://localhost:8080/api/v1/chat", 
                               json=chat_data, 
                               timeout=30)
        print(f"Chat endpoint status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Chat response: {json.dumps(data, indent=2)}")
        else:
            print(f"Chat endpoint failed: {response.text}")
    except Exception as e:
        print(f"❌ Chat endpoint error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    # Wait a bit for server to start
    print("Waiting 5 seconds for server to start...")
    time.sleep(5)
    
    success = test_server_connection()
    if success:
        print("✅ Server is working correctly")
    else:
        print("❌ Server has issues")
