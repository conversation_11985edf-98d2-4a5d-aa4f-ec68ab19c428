"""
A.T.L.A.S Options Flow Analysis Module
Neural network for unusual options activity detection with automated vertical spread trading
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import json
import aiohttp
from collections import defaultdict

# Try to import ML libraries, fallback to simple analysis if not available
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import Dense, Dropout
    from sklearn.preprocessing import StandardScaler
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

from config import settings
from atlas_performance_optimizer import performance_optimizer


@dataclass
class OptionsFlowData:
    """Options flow data point"""
    symbol: str
    option_type: str  # 'call' or 'put'
    strike: float
    expiration: str
    volume: int
    open_interest: int
    implied_volatility: float
    delta: float
    premium: float
    unusual_activity_score: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'option_type': self.option_type,
            'strike': self.strike,
            'expiration': self.expiration,
            'volume': self.volume,
            'open_interest': self.open_interest,
            'implied_volatility': self.implied_volatility,
            'delta': self.delta,
            'premium': self.premium,
            'unusual_activity_score': self.unusual_activity_score,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class OptionsSignal:
    """Options flow trading signal"""
    symbol: str
    signal_type: str  # 'bullish', 'bearish', 'neutral'
    confidence: float
    recommended_strategy: str  # 'bull_call_spread', 'bear_put_spread', etc.
    entry_strikes: List[float]
    expiration: str
    max_risk: float
    max_reward: float
    probability_profit: float
    unusual_flow_indicators: List[str]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'signal_type': self.signal_type,
            'confidence': self.confidence,
            'recommended_strategy': self.recommended_strategy,
            'entry_strikes': self.entry_strikes,
            'expiration': self.expiration,
            'max_risk': self.max_risk,
            'max_reward': self.max_reward,
            'probability_profit': self.probability_profit,
            'unusual_flow_indicators': self.unusual_flow_indicators,
            'timestamp': self.timestamp.isoformat()
        }


class OptionsFlowAnalyzer:
    """
    Options flow analysis with neural network classification and automated spread trading
    """
    
    def __init__(self, market_engine, trading_engine):
        self.logger = logging.getLogger(__name__)
        self.market_engine = market_engine
        self.trading_engine = trading_engine
        
        # Neural network model
        self.model = None
        self.scaler = StandardScaler() if ML_AVAILABLE else None
        self.feature_columns = [
            'call_put_ratio', 'volume_oi_ratio', 'iv_rank', 'delta_weighted_volume',
            'premium_volume', 'time_to_expiration', 'moneyness', 'volume_spike'
        ]
        
        # API configuration
        self.fmp_api_key = settings.FMP_API_KEY
        
        # Trading thresholds
        self.confidence_threshold = 0.8  # 80% confidence for trading
        self.max_position_risk = 0.05    # 5% of account per position
        self.min_reward_risk_ratio = 2.0 # Minimum 2:1 reward:risk
        
        # Liquid options universe (top 100 most liquid)
        self.liquid_symbols = [
            'SPY', 'QQQ', 'IWM', 'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META',
            'NFLX', 'AMD', 'CRM', 'ADBE', 'PYPL', 'INTC', 'CSCO', 'PEP', 'KO', 'DIS',
            'BA', 'JPM', 'GS', 'MS', 'BAC', 'WFC', 'C', 'V', 'MA', 'XOM'
        ]
        
        # Options flow cache
        self.flow_cache = {}
        self.cache_duration = 300  # 5 minutes
        
        # Initialize model
        if ML_AVAILABLE:
            self._initialize_model()
        
        self.logger.info(f"📊 Options Flow Analyzer initialized (ML: {'✅' if ML_AVAILABLE else '❌'})")
    
    def _initialize_model(self):
        """Initialize neural network model for options flow classification"""
        try:
            self.model = Sequential([
                Dense(64, activation='relu', input_shape=(len(self.feature_columns),)),
                Dropout(0.3),
                Dense(32, activation='relu'),
                Dropout(0.2),
                Dense(16, activation='relu'),
                Dense(3, activation='softmax')  # 3 classes: bullish, bearish, neutral
            ])
            
            self.model.compile(
                optimizer='adam',
                loss='categorical_crossentropy',
                metrics=['accuracy']
            )
            
            self.logger.info("✅ Options flow neural network initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing options model: {e}")
            self.model = None
    
    @performance_optimizer.performance_monitor("analyze_options_flow")
    async def analyze_options_flow(self, symbol: str) -> Optional[OptionsSignal]:
        """Analyze options flow for unusual activity and generate trading signals"""
        try:
            # Check if symbol is in liquid universe
            if symbol not in self.liquid_symbols:
                self.logger.warning(f"Symbol {symbol} not in liquid options universe")
                return None
            
            # Check cache
            cache_key = f"options_flow_{symbol}"
            if self._is_cached(cache_key):
                return self.flow_cache[cache_key]
            
            # Get options flow data
            flow_data = await self._get_options_flow_data(symbol)
            
            if not flow_data:
                self.logger.warning(f"No options flow data for {symbol}")
                return None
            
            # Extract features for ML model
            features = self._extract_flow_features(flow_data)
            
            if not features:
                return None
            
            # Generate signal using ML model or rule-based approach
            if ML_AVAILABLE and self.model:
                signal = self._ml_generate_signal(symbol, features, flow_data)
            else:
                signal = self._rule_based_signal(symbol, features, flow_data)
            
            # Cache result
            if signal:
                self.flow_cache[cache_key] = signal
                self.logger.info(f"📊 Options flow signal for {symbol}: {signal.signal_type} ({signal.confidence:.2f})")
            
            return signal
            
        except Exception as e:
            self.logger.error(f"Error analyzing options flow for {symbol}: {e}")
            return None
    
    async def _get_options_flow_data(self, symbol: str) -> List[OptionsFlowData]:
        """Get options flow data from FMP API"""
        try:
            # Get options chain data
            url = f"https://financialmodelingprep.com/api/v3/options-chain/{symbol}"
            params = {'apikey': self.fmp_api_key}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        options_data = await response.json()
                    else:
                        self.logger.warning(f"Options API error for {symbol}: {response.status}")
                        return []
            
            # Convert to OptionsFlowData objects
            flow_data = []
            current_time = datetime.now()
            
            for option in options_data[:50]:  # Limit to 50 most active options
                try:
                    # Calculate unusual activity score (simplified)
                    volume = option.get('volume', 0)
                    open_interest = option.get('openInterest', 1)
                    avg_volume = open_interest * 0.1  # Assume 10% of OI is normal volume
                    
                    unusual_score = min(volume / max(avg_volume, 1), 10.0)  # Cap at 10x
                    
                    flow_data.append(OptionsFlowData(
                        symbol=symbol,
                        option_type='call' if option.get('type') == 'call' else 'put',
                        strike=float(option.get('strike', 0)),
                        expiration=option.get('expiration', ''),
                        volume=volume,
                        open_interest=open_interest,
                        implied_volatility=float(option.get('impliedVolatility', 0)),
                        delta=float(option.get('delta', 0)),
                        premium=float(option.get('lastPrice', 0)),
                        unusual_activity_score=unusual_score,
                        timestamp=current_time
                    ))
                    
                except Exception as e:
                    continue
            
            return flow_data
            
        except Exception as e:
            self.logger.error(f"Error getting options flow data: {e}")
            return []
    
    def _extract_flow_features(self, flow_data: List[OptionsFlowData]) -> Optional[Dict[str, float]]:
        """Extract features from options flow data"""
        try:
            if not flow_data:
                return None
            
            # Separate calls and puts
            calls = [d for d in flow_data if d.option_type == 'call']
            puts = [d for d in flow_data if d.option_type == 'put']
            
            # Calculate call/put ratio
            call_volume = sum(d.volume for d in calls)
            put_volume = sum(d.volume for d in puts)
            call_put_ratio = call_volume / max(put_volume, 1)
            
            # Volume/OI ratio
            total_volume = sum(d.volume for d in flow_data)
            total_oi = sum(d.open_interest for d in flow_data)
            volume_oi_ratio = total_volume / max(total_oi, 1)
            
            # IV rank (simplified)
            iv_values = [d.implied_volatility for d in flow_data if d.implied_volatility > 0]
            iv_rank = np.percentile(iv_values, 50) if iv_values else 0.5
            
            # Delta-weighted volume
            delta_weighted_vol = sum(abs(d.delta) * d.volume for d in flow_data)
            
            # Premium volume
            premium_volume = sum(d.premium * d.volume for d in flow_data)
            
            # Average time to expiration (simplified)
            time_to_expiration = 30.0  # Assume 30 days average
            
            # Moneyness (simplified - would need current stock price)
            moneyness = 1.0  # Assume at-the-money
            
            # Volume spike indicator
            unusual_scores = [d.unusual_activity_score for d in flow_data]
            volume_spike = np.mean(unusual_scores) if unusual_scores else 1.0
            
            return {
                'call_put_ratio': call_put_ratio,
                'volume_oi_ratio': volume_oi_ratio,
                'iv_rank': iv_rank,
                'delta_weighted_volume': delta_weighted_vol,
                'premium_volume': premium_volume,
                'time_to_expiration': time_to_expiration,
                'moneyness': moneyness,
                'volume_spike': volume_spike
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting features: {e}")
            return None
    
    def _ml_generate_signal(self, symbol: str, features: Dict[str, float], 
                           flow_data: List[OptionsFlowData]) -> Optional[OptionsSignal]:
        """Generate signal using ML model"""
        try:
            # Prepare features for model
            feature_array = np.array([[features[col] for col in self.feature_columns]])
            
            # Normalize features
            normalized_features = self.scaler.fit_transform(feature_array)
            
            # Get prediction
            prediction = self.model.predict(normalized_features, verbose=0)
            
            # Convert to signal
            class_probs = prediction[0]
            predicted_class = np.argmax(class_probs)
            confidence = float(class_probs[predicted_class])
            
            signal_types = ['bearish', 'neutral', 'bullish']
            signal_type = signal_types[predicted_class]
            
            if confidence < self.confidence_threshold:
                signal_type = 'neutral'
            
            # Generate trading strategy recommendation
            strategy, strikes, expiration = self._recommend_strategy(signal_type, flow_data)
            
            # Calculate risk/reward (simplified)
            max_risk = 100.0  # $100 per spread
            max_reward = 200.0  # $200 max profit
            prob_profit = confidence * 0.6  # Adjust confidence to probability
            
            return OptionsSignal(
                symbol=symbol,
                signal_type=signal_type,
                confidence=confidence,
                recommended_strategy=strategy,
                entry_strikes=strikes,
                expiration=expiration,
                max_risk=max_risk,
                max_reward=max_reward,
                probability_profit=prob_profit,
                unusual_flow_indicators=self._identify_unusual_indicators(flow_data),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Error in ML signal generation: {e}")
            return None
    
    def _rule_based_signal(self, symbol: str, features: Dict[str, float], 
                          flow_data: List[OptionsFlowData]) -> Optional[OptionsSignal]:
        """Generate signal using rule-based approach"""
        try:
            # Rule-based classification
            call_put_ratio = features['call_put_ratio']
            volume_spike = features['volume_spike']
            
            # Determine signal type
            if call_put_ratio > 2.0 and volume_spike > 3.0:
                signal_type = 'bullish'
                confidence = min(0.9, (call_put_ratio + volume_spike) / 10.0)
            elif call_put_ratio < 0.5 and volume_spike > 3.0:
                signal_type = 'bearish'
                confidence = min(0.9, (2.0 - call_put_ratio + volume_spike) / 10.0)
            else:
                signal_type = 'neutral'
                confidence = 0.5
            
            # Generate strategy recommendation
            strategy, strikes, expiration = self._recommend_strategy(signal_type, flow_data)
            
            return OptionsSignal(
                symbol=symbol,
                signal_type=signal_type,
                confidence=confidence,
                recommended_strategy=strategy,
                entry_strikes=strikes,
                expiration=expiration,
                max_risk=100.0,
                max_reward=200.0,
                probability_profit=confidence * 0.6,
                unusual_flow_indicators=self._identify_unusual_indicators(flow_data),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Error in rule-based signal generation: {e}")
            return None
    
    def _recommend_strategy(self, signal_type: str, flow_data: List[OptionsFlowData]) -> Tuple[str, List[float], str]:
        """Recommend options strategy based on signal"""
        try:
            if not flow_data:
                return 'no_strategy', [], ''
            
            # Get most active options for strike selection
            sorted_options = sorted(flow_data, key=lambda x: x.volume, reverse=True)
            
            if signal_type == 'bullish':
                strategy = 'bull_call_spread'
                # Select strikes for bull call spread
                if len(sorted_options) >= 2:
                    strikes = [sorted_options[0].strike, sorted_options[1].strike]
                    strikes.sort()  # Lower strike first
                else:
                    strikes = [100.0, 105.0]  # Default strikes
                    
            elif signal_type == 'bearish':
                strategy = 'bear_put_spread'
                # Select strikes for bear put spread
                if len(sorted_options) >= 2:
                    strikes = [sorted_options[0].strike, sorted_options[1].strike]
                    strikes.sort(reverse=True)  # Higher strike first
                else:
                    strikes = [105.0, 100.0]  # Default strikes
            else:
                strategy = 'no_strategy'
                strikes = []
            
            # Get expiration from most active option
            expiration = sorted_options[0].expiration if sorted_options else ''
            
            return strategy, strikes, expiration
            
        except Exception as e:
            self.logger.error(f"Error recommending strategy: {e}")
            return 'no_strategy', [], ''
    
    def _identify_unusual_indicators(self, flow_data: List[OptionsFlowData]) -> List[str]:
        """Identify unusual flow indicators"""
        indicators = []
        
        try:
            # High volume spike
            high_volume_options = [d for d in flow_data if d.unusual_activity_score > 5.0]
            if high_volume_options:
                indicators.append(f"High volume spike in {len(high_volume_options)} options")
            
            # Unusual call/put activity
            calls = [d for d in flow_data if d.option_type == 'call']
            puts = [d for d in flow_data if d.option_type == 'put']
            
            call_volume = sum(d.volume for d in calls)
            put_volume = sum(d.volume for d in puts)
            
            if call_volume > put_volume * 3:
                indicators.append("Heavy call buying activity")
            elif put_volume > call_volume * 3:
                indicators.append("Heavy put buying activity")
            
            # Large premium transactions
            large_premium = [d for d in flow_data if d.premium * d.volume > 10000]
            if large_premium:
                indicators.append(f"Large premium transactions: ${sum(d.premium * d.volume for d in large_premium):,.0f}")
            
            return indicators

        except Exception as e:
            self.logger.error(f"Error identifying indicators: {e}")
            return []

    async def execute_options_signal(self, signal: OptionsSignal) -> Optional[Dict[str, Any]]:
        """Execute options trading signal with spread strategy"""
        try:
            if signal.signal_type == 'neutral' or signal.confidence < self.confidence_threshold:
                return None

            # Check risk/reward ratio
            if signal.max_reward / max(signal.max_risk, 1) < self.min_reward_risk_ratio:
                self.logger.warning(f"Poor risk/reward ratio for {signal.symbol}: {signal.max_reward/signal.max_risk:.2f}")
                return None

            # Execute spread strategy
            if signal.recommended_strategy == 'bull_call_spread':
                result = await self._execute_bull_call_spread(signal)
            elif signal.recommended_strategy == 'bear_put_spread':
                result = await self._execute_bear_put_spread(signal)
            else:
                self.logger.warning(f"Unknown strategy: {signal.recommended_strategy}")
                return None

            if result and result.get('success'):
                self.logger.info(f"✅ Options spread executed: {signal.recommended_strategy} on {signal.symbol}")
            else:
                self.logger.error(f"❌ Options spread failed: {signal.recommended_strategy} on {signal.symbol}")

            return result

        except Exception as e:
            self.logger.error(f"Error executing options signal: {e}")
            return None

    async def _execute_bull_call_spread(self, signal: OptionsSignal) -> Optional[Dict[str, Any]]:
        """Execute bull call spread strategy"""
        try:
            if len(signal.entry_strikes) < 2:
                return None

            lower_strike = min(signal.entry_strikes)
            higher_strike = max(signal.entry_strikes)

            # Calculate position size (limit to 5% of account)
            account_value = await self._get_account_value()
            max_risk_amount = account_value * self.max_position_risk
            contracts = max(1, int(max_risk_amount / signal.max_risk))

            # Buy lower strike call
            buy_order = await self.trading_engine.place_options_order(
                symbol=signal.symbol,
                option_type='call',
                strike=lower_strike,
                expiration=signal.expiration,
                side='buy',
                quantity=contracts,
                order_type='market'
            )

            if not buy_order or not buy_order.get('success'):
                return {'success': False, 'error': 'Failed to buy long call'}

            # Sell higher strike call
            sell_order = await self.trading_engine.place_options_order(
                symbol=signal.symbol,
                option_type='call',
                strike=higher_strike,
                expiration=signal.expiration,
                side='sell',
                quantity=contracts,
                order_type='market'
            )

            if not sell_order or not sell_order.get('success'):
                # Cancel the buy order if sell fails
                await self.trading_engine.cancel_order(buy_order.get('order_id'))
                return {'success': False, 'error': 'Failed to sell short call, position cancelled'}

            return {
                'success': True,
                'strategy': 'bull_call_spread',
                'symbol': signal.symbol,
                'contracts': contracts,
                'long_leg': buy_order,
                'short_leg': sell_order,
                'max_risk': signal.max_risk * contracts,
                'max_reward': signal.max_reward * contracts,
                'expiration': signal.expiration
            }

        except Exception as e:
            self.logger.error(f"Error executing bull call spread: {e}")
            return None

    async def _execute_bear_put_spread(self, signal: OptionsSignal) -> Optional[Dict[str, Any]]:
        """Execute bear put spread strategy"""
        try:
            if len(signal.entry_strikes) < 2:
                return None

            higher_strike = max(signal.entry_strikes)
            lower_strike = min(signal.entry_strikes)

            # Calculate position size
            account_value = await self._get_account_value()
            max_risk_amount = account_value * self.max_position_risk
            contracts = max(1, int(max_risk_amount / signal.max_risk))

            # Buy higher strike put
            buy_order = await self.trading_engine.place_options_order(
                symbol=signal.symbol,
                option_type='put',
                strike=higher_strike,
                expiration=signal.expiration,
                side='buy',
                quantity=contracts,
                order_type='market'
            )

            if not buy_order or not buy_order.get('success'):
                return {'success': False, 'error': 'Failed to buy long put'}

            # Sell lower strike put
            sell_order = await self.trading_engine.place_options_order(
                symbol=signal.symbol,
                option_type='put',
                strike=lower_strike,
                expiration=signal.expiration,
                side='sell',
                quantity=contracts,
                order_type='market'
            )

            if not sell_order or not sell_order.get('success'):
                # Cancel the buy order if sell fails
                await self.trading_engine.cancel_order(buy_order.get('order_id'))
                return {'success': False, 'error': 'Failed to sell short put, position cancelled'}

            return {
                'success': True,
                'strategy': 'bear_put_spread',
                'symbol': signal.symbol,
                'contracts': contracts,
                'long_leg': buy_order,
                'short_leg': sell_order,
                'max_risk': signal.max_risk * contracts,
                'max_reward': signal.max_reward * contracts,
                'expiration': signal.expiration
            }

        except Exception as e:
            self.logger.error(f"Error executing bear put spread: {e}")
            return None

    async def _get_account_value(self) -> float:
        """Get current account value for position sizing"""
        try:
            account_info = await self.trading_engine.get_account_info()
            return float(account_info.get('equity', 10000))  # Default to $10k if not available
        except Exception as e:
            self.logger.error(f"Error getting account value: {e}")
            return 10000.0  # Default fallback

    async def batch_analyze_options_flow(self, symbols: List[str]) -> List[OptionsSignal]:
        """Batch analyze options flow for multiple symbols"""
        try:
            signals = []

            # Process in smaller batches to avoid API limits
            batch_size = 10
            for i in range(0, len(symbols), batch_size):
                batch_symbols = symbols[i:i + batch_size]

                # Create tasks for concurrent processing
                tasks = [self.analyze_options_flow(symbol) for symbol in batch_symbols]
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)

                # Filter successful results
                for result in batch_results:
                    if isinstance(result, OptionsSignal):
                        signals.append(result)
                    elif isinstance(result, Exception):
                        self.logger.error(f"Batch analysis error: {result}")

                # Small delay between batches
                await asyncio.sleep(1)

            self.logger.info(f"📊 Batch options analysis completed: {len(signals)} signals")
            return signals

        except Exception as e:
            self.logger.error(f"Error in batch options analysis: {e}")
            return []

    def _is_cached(self, cache_key: str) -> bool:
        """Check if data is cached and still valid"""
        if cache_key not in self.flow_cache:
            return False

        cached_data = self.flow_cache[cache_key]
        if (datetime.now() - cached_data.timestamp).seconds > self.cache_duration:
            del self.flow_cache[cache_key]
            return False

        return True

    async def get_options_flow_status(self) -> Dict[str, Any]:
        """Get options flow analyzer status"""
        return {
            'ml_available': ML_AVAILABLE,
            'model_loaded': self.model is not None,
            'liquid_symbols_count': len(self.liquid_symbols),
            'cache_size': len(self.flow_cache),
            'thresholds': {
                'confidence_threshold': self.confidence_threshold,
                'max_position_risk': self.max_position_risk,
                'min_reward_risk_ratio': self.min_reward_risk_ratio
            },
            'supported_strategies': [
                'bull_call_spread',
                'bear_put_spread'
            ],
            'feature_columns': self.feature_columns
        }

    async def get_top_options_opportunities(self, limit: int = 10) -> List[OptionsSignal]:
        """Get top options flow opportunities"""
        try:
            # Analyze top liquid symbols
            top_symbols = self.liquid_symbols[:20]  # Analyze top 20 liquid symbols
            signals = await self.batch_analyze_options_flow(top_symbols)

            # Filter and sort by confidence
            high_confidence_signals = [
                signal for signal in signals
                if signal.confidence >= self.confidence_threshold and signal.signal_type != 'neutral'
            ]

            # Sort by confidence and return top opportunities
            sorted_signals = sorted(high_confidence_signals, key=lambda x: x.confidence, reverse=True)

            return sorted_signals[:limit]

        except Exception as e:
            self.logger.error(f"Error getting top options opportunities: {e}")
            return []

    def clear_cache(self):
        """Clear options flow cache"""
        self.flow_cache.clear()
        self.logger.info("🧹 Options flow cache cleared")
