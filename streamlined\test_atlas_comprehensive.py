"""
Comprehensive A.T.L.A.S System Testing Suite
Tests all enhanced features, performance, and success criteria validation
"""

import asyncio
import pytest
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any
import json

# Import A.T.L.A.S components
from atlas_orchestrator import AtlasOrchestrator
from atlas_market_engine import AtlasMarketEngine
from atlas_ai_engine import AtlasAIEngine, EmotionalState, TradingGoal
from atlas_realtime_scanner import RealTimeTTMScanner, SignalStrength
from atlas_proactive_assistant import ProactiveTradingAssistant, AlertType, AlertPriority
from atlas_market_context import RealTimeMarketContextEngine

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PerformanceTracker:
    """Track performance metrics during testing"""
    
    def __init__(self):
        self.response_times = []
        self.errors = []
        self.start_time = None
        self.uptime_checks = []
    
    def start_timing(self):
        """Start timing a request"""
        self.start_time = time.time()
    
    def end_timing(self, operation: str):
        """End timing and record response time"""
        if self.start_time:
            response_time = time.time() - self.start_time
            self.response_times.append({
                'operation': operation,
                'response_time': response_time,
                'timestamp': datetime.now()
            })
            self.start_time = None
            return response_time
        return None
    
    def record_error(self, operation: str, error: str):
        """Record an error"""
        self.errors.append({
            'operation': operation,
            'error': error,
            'timestamp': datetime.now()
        })
    
    def record_uptime_check(self, is_healthy: bool):
        """Record uptime check result"""
        self.uptime_checks.append({
            'healthy': is_healthy,
            'timestamp': datetime.now()
        })
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        if not self.response_times:
            return {'error': 'No performance data collected'}
        
        response_times = [rt['response_time'] for rt in self.response_times]
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        min_response_time = min(response_times)
        
        # Calculate uptime percentage
        uptime_percentage = 0.0
        if self.uptime_checks:
            healthy_checks = sum(1 for check in self.uptime_checks if check['healthy'])
            uptime_percentage = (healthy_checks / len(self.uptime_checks)) * 100
        
        return {
            'total_requests': len(self.response_times),
            'avg_response_time': avg_response_time,
            'max_response_time': max_response_time,
            'min_response_time': min_response_time,
            'sub_2_second_percentage': (sum(1 for rt in response_times if rt < 2.0) / len(response_times)) * 100,
            'total_errors': len(self.errors),
            'error_rate': (len(self.errors) / len(self.response_times)) * 100 if self.response_times else 0,
            'uptime_percentage': uptime_percentage,
            'uptime_checks': len(self.uptime_checks)
        }


# Global performance tracker
performance_tracker = PerformanceTracker()


@pytest.fixture
async def atlas_orchestrator():
    """Create A.T.L.A.S orchestrator for testing"""
    orchestrator = AtlasOrchestrator(mentor_mode=True)
    yield orchestrator
    # Cleanup
    try:
        await orchestrator.cleanup()
    except:
        pass


@pytest.fixture
async def market_engine():
    """Create market engine for testing"""
    engine = AtlasMarketEngine()
    yield engine
    # Cleanup
    try:
        await engine.cleanup()
    except:
        pass


class TestPerformanceRequirements:
    """Test performance requirements (<2 second response times)"""
    
    @pytest.mark.asyncio
    async def test_chat_response_time(self, atlas_orchestrator):
        """Test chat response time is under 2 seconds"""
        test_messages = [
            "What's the market looking like today?",
            "Analyze AAPL for me",
            "Should I buy TSLA?",
            "What are the top opportunities right now?",
            "Help me understand TTM Squeeze"
        ]
        
        for message in test_messages:
            performance_tracker.start_timing()
            
            try:
                response = await atlas_orchestrator.process_message(message)
                response_time = performance_tracker.end_timing(f"chat: {message[:20]}")
                
                # Assert response time is under 2 seconds
                assert response_time < 2.0, f"Response time {response_time:.2f}s exceeds 2 second requirement"
                assert response.response is not None, "Response should not be None"
                
                logger.info(f"✅ Chat response time: {response_time:.2f}s for '{message[:30]}...'")
                
            except Exception as e:
                performance_tracker.record_error(f"chat: {message[:20]}", str(e))
                pytest.fail(f"Chat failed for message '{message}': {e}")
    
    @pytest.mark.asyncio
    async def test_market_analysis_performance(self, market_engine):
        """Test market analysis performance"""
        symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
        
        for symbol in symbols:
            performance_tracker.start_timing()
            
            try:
                analysis = await market_engine.get_comprehensive_analysis(symbol)
                response_time = performance_tracker.end_timing(f"analysis: {symbol}")
                
                # Assert response time is under 2 seconds
                assert response_time < 2.0, f"Analysis time {response_time:.2f}s exceeds 2 second requirement"
                assert 'symbol' in analysis, "Analysis should contain symbol"
                
                logger.info(f"✅ Analysis response time: {response_time:.2f}s for {symbol}")
                
            except Exception as e:
                performance_tracker.record_error(f"analysis: {symbol}", str(e))
                pytest.fail(f"Analysis failed for {symbol}: {e}")
    
    @pytest.mark.asyncio
    async def test_scanner_performance(self, market_engine):
        """Test real-time scanner performance"""
        performance_tracker.start_timing()
        
        try:
            # Test getting live signals
            signals = market_engine.get_live_ttm_signals(min_strength=3)
            response_time = performance_tracker.end_timing("scanner: live_signals")
            
            # Assert response time is under 1 second for scanner
            assert response_time < 1.0, f"Scanner time {response_time:.2f}s exceeds 1 second requirement"
            assert isinstance(signals, list), "Signals should be a list"
            
            logger.info(f"✅ Scanner response time: {response_time:.2f}s")
            
        except Exception as e:
            performance_tracker.record_error("scanner: live_signals", str(e))
            pytest.fail(f"Scanner test failed: {e}")


class TestFunctionalRequirements:
    """Test all functional requirements and features"""
    
    @pytest.mark.asyncio
    async def test_emotional_intelligence(self, atlas_orchestrator):
        """Test emotional intelligence and behavioral detection"""
        test_cases = [
            {
                'message': "I lost money on TSLA, need to make it back fast!",
                'expected_warnings': ['revenge_trading']
            },
            {
                'message': "Everyone else is making money, I'm missing out!",
                'expected_warnings': ['fomo_trading']
            },
            {
                'message': "This is guaranteed to work, easy money!",
                'expected_warnings': ['overconfident']
            }
        ]
        
        for test_case in test_cases:
            response = await atlas_orchestrator.process_message(test_case['message'])
            
            # Check that emotional coaching is present
            assert any(keyword in response.response.lower() for keyword in ['psychology', 'emotion', 'careful', 'risk']), \
                f"Emotional coaching missing for: {test_case['message']}"
            
            logger.info(f"✅ Emotional intelligence detected for: {test_case['message'][:30]}...")
    
    @pytest.mark.asyncio
    async def test_goal_parsing_and_filtering(self, atlas_orchestrator):
        """Test goal parsing and result filtering"""
        goal_messages = [
            "I want to make $500 today with moderate risk",
            "Help me earn $1000 this week, I'm conservative",
            "I need to make $100 quickly but safely"
        ]
        
        for message in goal_messages:
            response = await atlas_orchestrator.process_message(message)
            
            # Check that goal was understood and risk management is mentioned
            assert any(keyword in response.response.lower() for keyword in ['goal', 'target', 'risk', 'safe']), \
                f"Goal parsing failed for: {message}"
            
            logger.info(f"✅ Goal parsing successful for: {message[:30]}...")
    
    @pytest.mark.asyncio
    async def test_real_time_scanner(self, market_engine):
        """Test real-time TTM Squeeze scanner"""
        try:
            # Test scanner status
            status = market_engine.get_scanner_status()
            assert 'total_symbols' in status or 'error' in status, "Scanner status should have data"
            
            # Test getting signals by strength
            for strength in [3, 4, 5]:
                signals = market_engine.get_live_ttm_signals(min_strength=strength)
                assert isinstance(signals, list), f"Signals should be list for strength {strength}"
            
            logger.info("✅ Real-time scanner functional")
            
        except Exception as e:
            pytest.fail(f"Scanner test failed: {e}")
    
    @pytest.mark.asyncio
    async def test_market_context_integration(self, market_engine):
        """Test market context integration"""
        try:
            # Test getting market context
            context = await market_engine.get_market_context()
            assert isinstance(context, dict), "Market context should be dictionary"
            
            # Test context in analysis
            analysis = await market_engine.get_comprehensive_analysis('SPY')
            assert 'market_context' in analysis, "Analysis should include market context"
            
            logger.info("✅ Market context integration functional")
            
        except Exception as e:
            pytest.fail(f"Market context test failed: {e}")
    
    @pytest.mark.asyncio
    async def test_proactive_assistant(self, market_engine):
        """Test proactive assistant features"""
        try:
            # Test proactive status
            status = market_engine.get_proactive_status()
            assert isinstance(status, dict), "Proactive status should be dictionary"
            
            # Test custom alert
            success = await market_engine.send_custom_alert(
                "Test Alert", "This is a test alert", "medium"
            )
            assert isinstance(success, bool), "Alert sending should return boolean"
            
            # Test getting alerts
            alerts = market_engine.get_active_alerts()
            assert isinstance(alerts, list), "Alerts should be list"
            
            logger.info("✅ Proactive assistant functional")
            
        except Exception as e:
            pytest.fail(f"Proactive assistant test failed: {e}")


class TestSuccessCriteria:
    """Test against specific success criteria"""
    
    @pytest.mark.asyncio
    async def test_response_time_criteria(self):
        """Test <2 second response time criteria"""
        summary = performance_tracker.get_performance_summary()
        
        if 'error' not in summary:
            # Assert average response time is under 2 seconds
            assert summary['avg_response_time'] < 2.0, \
                f"Average response time {summary['avg_response_time']:.2f}s exceeds 2 second requirement"
            
            # Assert 95% of requests are under 2 seconds
            assert summary['sub_2_second_percentage'] >= 95.0, \
                f"Only {summary['sub_2_second_percentage']:.1f}% of requests under 2 seconds (need 95%)"
            
            logger.info(f"✅ Response time criteria met: {summary['avg_response_time']:.2f}s average")
    
    @pytest.mark.asyncio
    async def test_uptime_criteria(self):
        """Test 99.9% uptime criteria"""
        # Simulate uptime checks
        for _ in range(100):
            try:
                # Simple health check
                orchestrator = AtlasOrchestrator(mentor_mode=True)
                performance_tracker.record_uptime_check(True)
            except:
                performance_tracker.record_uptime_check(False)
        
        summary = performance_tracker.get_performance_summary()
        
        if summary['uptime_checks'] > 0:
            # Assert uptime is above 99.9%
            assert summary['uptime_percentage'] >= 99.9, \
                f"Uptime {summary['uptime_percentage']:.2f}% below 99.9% requirement"
            
            logger.info(f"✅ Uptime criteria met: {summary['uptime_percentage']:.2f}%")
    
    @pytest.mark.asyncio
    async def test_error_rate_criteria(self):
        """Test low error rate criteria"""
        summary = performance_tracker.get_performance_summary()
        
        if 'error' not in summary and summary['total_requests'] > 0:
            # Assert error rate is below 5%
            assert summary['error_rate'] < 5.0, \
                f"Error rate {summary['error_rate']:.2f}% exceeds 5% threshold"
            
            logger.info(f"✅ Error rate criteria met: {summary['error_rate']:.2f}%")


@pytest.mark.asyncio
async def test_comprehensive_system():
    """Comprehensive system test"""
    logger.info("🚀 Starting comprehensive A.T.L.A.S system test...")
    
    try:
        # Initialize orchestrator
        orchestrator = AtlasOrchestrator(mentor_mode=True)
        
        # Test various scenarios
        test_scenarios = [
            "What's the market sentiment today?",
            "Analyze AAPL and tell me if I should buy",
            "I want to make $200 today, what are my options?",
            "I'm feeling anxious about my trades, help me",
            "Show me the top TTM Squeeze opportunities"
        ]
        
        for scenario in test_scenarios:
            performance_tracker.start_timing()
            response = await orchestrator.process_message(scenario)
            response_time = performance_tracker.end_timing(f"comprehensive: {scenario[:20]}")
            
            assert response.response is not None, f"No response for: {scenario}"
            assert len(response.response) > 10, f"Response too short for: {scenario}"
            
            logger.info(f"✅ Scenario passed ({response_time:.2f}s): {scenario[:40]}...")
        
        # Cleanup
        await orchestrator.cleanup()
        
        logger.info("✅ Comprehensive system test completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Comprehensive system test failed: {e}")
        pytest.fail(f"Comprehensive test failed: {e}")


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
    
    # Print performance summary
    summary = performance_tracker.get_performance_summary()
    print("\n" + "="*50)
    print("A.T.L.A.S PERFORMANCE SUMMARY")
    print("="*50)
    
    if 'error' not in summary:
        print(f"Total Requests: {summary['total_requests']}")
        print(f"Average Response Time: {summary['avg_response_time']:.3f}s")
        print(f"Max Response Time: {summary['max_response_time']:.3f}s")
        print(f"Sub-2s Percentage: {summary['sub_2_second_percentage']:.1f}%")
        print(f"Error Rate: {summary['error_rate']:.2f}%")
        print(f"Uptime: {summary['uptime_percentage']:.2f}%")
        
        # Success criteria check
        print("\n" + "="*30)
        print("SUCCESS CRITERIA VALIDATION")
        print("="*30)
        
        criteria_met = 0
        total_criteria = 3
        
        if summary['avg_response_time'] < 2.0:
            print("✅ Response Time: PASSED (<2s)")
            criteria_met += 1
        else:
            print("❌ Response Time: FAILED (≥2s)")
        
        if summary['uptime_percentage'] >= 99.9:
            print("✅ Uptime: PASSED (≥99.9%)")
            criteria_met += 1
        else:
            print("❌ Uptime: FAILED (<99.9%)")
        
        if summary['error_rate'] < 5.0:
            print("✅ Error Rate: PASSED (<5%)")
            criteria_met += 1
        else:
            print("❌ Error Rate: FAILED (≥5%)")
        
        print(f"\nOverall: {criteria_met}/{total_criteria} criteria met")
        
        if criteria_met == total_criteria:
            print("🎉 ALL SUCCESS CRITERIA MET!")
        else:
            print("⚠️  Some criteria need attention")
    else:
        print("No performance data available")
