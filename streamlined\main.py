"""
A.T.L.A.S AI Trading System - Main Entry Point
Provides startup checks and system initialization
"""

import sys
import os
import logging
import asyncio
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from config import settings


def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    
    # Core dependencies
    try:
        import fastapi
        import uvicorn
        import pydantic
        import aiohttp
        import requests
        import alpaca_trade_api
        import openai
        import numpy
        import pandas
    except ImportError as e:
        missing_deps.append(f"Core dependency: {e}")
    
    # Optional ML dependencies
    ml_deps_available = True
    try:
        import tensorflow
        import scikit_learn
        import transformers
        import torch
    except ImportError:
        ml_deps_available = False
    
    if missing_deps:
        print("❌ Missing required dependencies:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\nPlease install missing dependencies:")
        print("   pip install -r requirements.txt")
        return False
    
    print("✅ All core dependencies available")
    if not ml_deps_available:
        print("⚠️  ML dependencies not available - advanced features will be disabled")
    else:
        print("✅ ML dependencies available - all features enabled")
    
    return True


def check_configuration():
    """Check if configuration is properly set up"""
    issues = []
    
    # Check API keys
    if not settings.OPENAI_API_KEY or settings.OPENAI_API_KEY == "your_openai_api_key":
        issues.append("OpenAI API key not configured")
    
    if not settings.APCA_API_KEY_ID or settings.APCA_API_KEY_ID == "your_alpaca_key":
        issues.append("Alpaca API key not configured")
    
    if not settings.FMP_API_KEY or settings.FMP_API_KEY == "your_fmp_api_key":
        issues.append("Financial Modeling Prep API key not configured")
    
    # Check environment file
    env_file = Path(".env")
    if not env_file.exists():
        issues.append(".env file not found - copy from .env.example")
    
    if issues:
        print("⚠️  Configuration issues found:")
        for issue in issues:
            print(f"   - {issue}")
        print("\nPlease check your .env file and API key configuration")
        return False
    
    print("✅ Configuration looks good")
    return True


def check_system_requirements():
    """Check system requirements"""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Check available memory (basic check)
    try:
        import psutil
        memory = psutil.virtual_memory()
        if memory.total < 2 * 1024 * 1024 * 1024:  # 2GB
            print("⚠️  Low memory detected - may affect ML performance")
        else:
            print(f"✅ Memory: {memory.total // (1024**3)}GB available")
    except ImportError:
        print("ℹ️  Memory check skipped (psutil not available)")
    
    return True


def startup_checks():
    """Perform all startup checks"""
    print("🚀 A.T.L.A.S AI Trading System - Startup Checks")
    print("=" * 50)
    
    checks = [
        ("System Requirements", check_system_requirements),
        ("Dependencies", check_dependencies),
        ("Configuration", check_configuration)
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}:")
        if not check_func():
            all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("✅ All startup checks passed!")
        print("🎯 Ready to start A.T.L.A.S AI Trading System")
        return True
    else:
        print("❌ Some startup checks failed")
        print("🔧 Please fix the issues above before starting the system")
        return False


def main():
    """Main entry point"""
    if not startup_checks():
        sys.exit(1)
    
    print("\n🚀 Starting A.T.L.A.S AI Trading System...")
    print("🎯 Advanced Trading & Learning Analysis System")
    print("🧠 Consolidated Architecture with Unified Orchestrator")
    
    # Import and start the server
    try:
        from atlas_server import app
        import uvicorn
        
        print(f"\n🌐 Starting server on http://localhost:{settings.PORT}")
        print("📚 API Documentation: http://localhost:{}/docs".format(settings.PORT))
        print("🎮 Web Interface: http://localhost:{}".format(settings.PORT))
        print("\n💡 Press Ctrl+C to stop the server")
        
        uvicorn.run(
            "atlas_server:app",
            host="0.0.0.0",
            port=settings.PORT,
            reload=True,
            log_level=settings.LOG_LEVEL.lower()
        )
        
    except KeyboardInterrupt:
        print("\n🛑 Shutting down A.T.L.A.S system...")
        print("👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        print("🔧 Please check the logs for more details")
        sys.exit(1)


if __name__ == "__main__":
    main()
