"""
A.T.L.A.S Multi-Source Sentiment Analysis Module
DistilBERT sentiment analysis from news/Reddit/Twitter with auto-hedging
"""

import asyncio
import logging
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import json
import re
from transformers import DistilBertTokenizer, DistilBertForSequenceClassification
import torch
import torch.nn.functional as F
from collections import defaultdict
import time

from config import settings
from atlas_performance_optimizer import performance_optimizer


@dataclass
class SentimentData:
    """Sentiment analysis result"""
    source: str  # 'news', 'reddit', 'twitter'
    symbol: str
    text: str
    sentiment_score: float  # -1 to 1
    confidence: float
    timestamp: datetime
    url: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'source': self.source,
            'symbol': self.symbol,
            'text': self.text[:200],  # Truncate for storage
            'sentiment_score': self.sentiment_score,
            'confidence': self.confidence,
            'timestamp': self.timestamp.isoformat(),
            'url': self.url
        }


@dataclass
class SentimentSignal:
    """Aggregated sentiment trading signal"""
    symbol: str
    overall_sentiment: float  # -1 to 1
    signal_strength: str  # 'strong_bullish', 'weak_bullish', 'neutral', 'weak_bearish', 'strong_bearish'
    confidence: float
    source_breakdown: Dict[str, float]
    data_points: int
    implied_volatility: Optional[float]
    should_hedge: bool
    hedge_symbol: Optional[str]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'overall_sentiment': self.overall_sentiment,
            'signal_strength': self.signal_strength,
            'confidence': self.confidence,
            'source_breakdown': self.source_breakdown,
            'data_points': self.data_points,
            'implied_volatility': self.implied_volatility,
            'should_hedge': self.should_hedge,
            'hedge_symbol': self.hedge_symbol,
            'timestamp': self.timestamp.isoformat()
        }


class MultiSourceSentimentAnalyzer:
    """
    Multi-source sentiment analysis with DistilBERT and auto-hedging
    """
    
    def __init__(self, market_engine, trading_engine):
        self.logger = logging.getLogger(__name__)
        self.market_engine = market_engine
        self.trading_engine = trading_engine
        
        # Initialize DistilBERT model
        self.tokenizer = None
        self.model = None
        self._initialize_model()
        
        # API configurations
        self.fmp_api_key = settings.FMP_API_KEY
        self.reddit_client_id = getattr(settings, 'REDDIT_CLIENT_ID', None)
        self.reddit_client_secret = getattr(settings, 'REDDIT_CLIENT_SECRET', None)
        self.twitter_bearer_token = getattr(settings, 'TWITTER_BEARER_TOKEN', None)
        
        # Sentiment thresholds
        self.bullish_threshold = 0.3
        self.bearish_threshold = -0.3
        self.iv_threshold = 0.02  # 2% implied volatility threshold
        
        # Hedging configuration
        self.hedge_mapping = {
            'SPY': 'SH',   # ProShares Short S&P500
            'QQQ': 'PSQ',  # ProShares Short QQQ
            'IWM': 'RWM',  # ProShares Short Russell2000
            'DIA': 'DOG'   # ProShares Short Dow30
        }
        
        # Rate limiting
        self.last_api_calls = defaultdict(float)
        self.api_rate_limits = {
            'fmp': 1.0,      # 1 second between calls
            'reddit': 2.0,   # 2 seconds between calls
            'twitter': 1.0   # 1 second between calls
        }
        
        # Cache for sentiment data
        self.sentiment_cache = {}
        self.cache_duration = 300  # 5 minutes
        
        self.logger.info("📊 Multi-Source Sentiment Analyzer initialized")
    
    def _initialize_model(self):
        """Initialize DistilBERT model for sentiment analysis"""
        try:
            model_name = "distilbert-base-uncased-finetuned-sst-2-english"
            self.tokenizer = DistilBertTokenizer.from_pretrained(model_name)
            self.model = DistilBertForSequenceClassification.from_pretrained(model_name)
            
            # Set to evaluation mode
            self.model.eval()
            
            self.logger.info("✅ DistilBERT model loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Error loading DistilBERT model: {e}")
            self.tokenizer = None
            self.model = None
    
    @performance_optimizer.performance_monitor("analyze_sentiment")
    async def analyze_symbol_sentiment(self, symbol: str) -> Optional[SentimentSignal]:
        """Analyze sentiment for a symbol from all sources"""
        try:
            # Check cache first
            cache_key = f"sentiment_{symbol}"
            if self._is_cached(cache_key):
                return self.sentiment_cache[cache_key]
            
            # Gather sentiment data from all sources
            sentiment_data = []
            
            # Get news sentiment
            news_data = await self._get_news_sentiment(symbol)
            sentiment_data.extend(news_data)
            
            # Get Reddit sentiment
            reddit_data = await self._get_reddit_sentiment(symbol)
            sentiment_data.extend(reddit_data)
            
            # Get Twitter sentiment (if available)
            if self.twitter_bearer_token:
                twitter_data = await self._get_twitter_sentiment(symbol)
                sentiment_data.extend(twitter_data)
            
            if not sentiment_data:
                self.logger.warning(f"No sentiment data found for {symbol}")
                return None
            
            # Aggregate sentiment
            signal = self._aggregate_sentiment(symbol, sentiment_data)
            
            # Get implied volatility for hedging decision
            signal.implied_volatility = await self._get_implied_volatility(symbol)
            
            # Determine hedging strategy
            signal.should_hedge, signal.hedge_symbol = self._should_hedge(symbol, signal)
            
            # Cache result
            self.sentiment_cache[cache_key] = signal
            
            self.logger.info(f"📊 Sentiment analysis for {symbol}: {signal.overall_sentiment:.3f} ({signal.signal_strength})")
            
            return signal
            
        except Exception as e:
            self.logger.error(f"Error analyzing sentiment for {symbol}: {e}")
            return None
    
    async def _get_news_sentiment(self, symbol: str) -> List[SentimentData]:
        """Get sentiment from financial news"""
        try:
            if not await self._check_rate_limit('fmp'):
                return []
            
            # Get news from Financial Modeling Prep
            url = f"https://financialmodelingprep.com/api/v3/stock_news"
            params = {
                'tickers': symbol,
                'limit': 20,
                'apikey': self.fmp_api_key
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        news_data = await response.json()
                    else:
                        self.logger.warning(f"News API error: {response.status}")
                        return []
            
            sentiment_results = []
            
            for article in news_data[:10]:  # Limit to 10 articles
                try:
                    # Combine title and text for analysis
                    text = f"{article.get('title', '')} {article.get('text', '')}"
                    
                    if len(text.strip()) < 10:
                        continue
                    
                    # Analyze sentiment
                    sentiment_score, confidence = self._analyze_text_sentiment(text)
                    
                    sentiment_results.append(SentimentData(
                        source='news',
                        symbol=symbol,
                        text=text,
                        sentiment_score=sentiment_score,
                        confidence=confidence,
                        timestamp=datetime.now(),
                        url=article.get('url')
                    ))
                    
                except Exception as e:
                    self.logger.error(f"Error processing news article: {e}")
                    continue
            
            return sentiment_results
            
        except Exception as e:
            self.logger.error(f"Error getting news sentiment: {e}")
            return []
    
    async def _get_reddit_sentiment(self, symbol: str) -> List[SentimentData]:
        """Get sentiment from Reddit finance subreddits"""
        try:
            if not self.reddit_client_id or not await self._check_rate_limit('reddit'):
                return []
            
            # Search relevant subreddits
            subreddits = ['wallstreetbets', 'stocks', 'investing', 'SecurityAnalysis']
            sentiment_results = []
            
            # This is a simplified implementation - in production, you'd use PRAW
            # For now, we'll simulate Reddit sentiment data
            
            # Simulate Reddit posts about the symbol
            simulated_posts = [
                f"{symbol} is looking bullish with strong fundamentals",
                f"Thinking of buying more {symbol} on this dip",
                f"{symbol} earnings report was disappointing",
                f"Technical analysis shows {symbol} breaking resistance"
            ]
            
            for post_text in simulated_posts:
                sentiment_score, confidence = self._analyze_text_sentiment(post_text)
                
                sentiment_results.append(SentimentData(
                    source='reddit',
                    symbol=symbol,
                    text=post_text,
                    sentiment_score=sentiment_score,
                    confidence=confidence,
                    timestamp=datetime.now()
                ))
            
            return sentiment_results
            
        except Exception as e:
            self.logger.error(f"Error getting Reddit sentiment: {e}")
            return []
    
    async def _get_twitter_sentiment(self, symbol: str) -> List[SentimentData]:
        """Get sentiment from Twitter"""
        try:
            if not self.twitter_bearer_token or not await self._check_rate_limit('twitter'):
                return []
            
            # Twitter API v2 search
            url = "https://api.twitter.com/2/tweets/search/recent"
            headers = {"Authorization": f"Bearer {self.twitter_bearer_token}"}
            params = {
                'query': f"${symbol} OR {symbol}",
                'max_results': 20,
                'tweet.fields': 'created_at,public_metrics'
            }
            
            sentiment_results = []
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        twitter_data = await response.json()
                        
                        for tweet in twitter_data.get('data', []):
                            text = tweet.get('text', '')
                            
                            if len(text.strip()) < 10:
                                continue
                            
                            sentiment_score, confidence = self._analyze_text_sentiment(text)
                            
                            sentiment_results.append(SentimentData(
                                source='twitter',
                                symbol=symbol,
                                text=text,
                                sentiment_score=sentiment_score,
                                confidence=confidence,
                                timestamp=datetime.now()
                            ))
                    else:
                        self.logger.warning(f"Twitter API error: {response.status}")
            
            return sentiment_results
            
        except Exception as e:
            self.logger.error(f"Error getting Twitter sentiment: {e}")
            return []
    
    def _analyze_text_sentiment(self, text: str) -> Tuple[float, float]:
        """Analyze sentiment of text using DistilBERT"""
        try:
            if not self.model or not self.tokenizer:
                # Fallback to simple keyword-based sentiment
                return self._simple_sentiment_analysis(text)
            
            # Clean and truncate text
            cleaned_text = self._clean_text(text)
            
            # Tokenize
            inputs = self.tokenizer(
                cleaned_text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )
            
            # Get prediction
            with torch.no_grad():
                outputs = self.model(**inputs)
                predictions = F.softmax(outputs.logits, dim=-1)
            
            # Convert to sentiment score (-1 to 1)
            negative_prob = predictions[0][0].item()
            positive_prob = predictions[0][1].item()
            
            sentiment_score = positive_prob - negative_prob
            confidence = max(positive_prob, negative_prob)
            
            return sentiment_score, confidence
            
        except Exception as e:
            self.logger.error(f"Error in sentiment analysis: {e}")
            return self._simple_sentiment_analysis(text)
    
    def _simple_sentiment_analysis(self, text: str) -> Tuple[float, float]:
        """Simple keyword-based sentiment analysis as fallback"""
        positive_words = ['bullish', 'buy', 'strong', 'good', 'positive', 'up', 'gain', 'profit']
        negative_words = ['bearish', 'sell', 'weak', 'bad', 'negative', 'down', 'loss', 'drop']
        
        text_lower = text.lower()
        
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        total_words = len(text.split())
        
        if total_words == 0:
            return 0.0, 0.0
        
        sentiment_score = (positive_count - negative_count) / max(total_words, 1)
        confidence = (positive_count + negative_count) / max(total_words, 1)
        
        return max(-1, min(1, sentiment_score)), min(1, confidence)
    
    def _clean_text(self, text: str) -> str:
        """Clean text for sentiment analysis"""
        # Remove URLs
        text = re.sub(r'http\S+', '', text)
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s.,!?]', '', text)
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        return text[:500]  # Truncate to 500 characters
