"""
A.T.L.A.S Proactive Trading Assistant
Provides morning briefings, real-time notifications, market protection, and time-sensitive alerts
"""

import asyncio
import logging
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
import json

from config import settings


class AlertPriority(Enum):
    """Alert priority levels"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


class AlertType(Enum):
    """Types of proactive alerts"""
    MORNING_BRIEFING = "morning_briefing"
    OPPORTUNITY_NOTIFICATION = "opportunity_notification"
    MARKET_PROTECTION = "market_protection"
    TIME_SENSITIVE_SETUP = "time_sensitive_setup"
    EARNINGS_WARNING = "earnings_warning"
    VOLATILITY_ALERT = "volatility_alert"


@dataclass
class ProactiveAlert:
    """Proactive alert data structure"""
    alert_type: AlertType
    priority: AlertPriority
    title: str
    message: str
    action_required: bool
    expiry_time: Optional[datetime]
    metadata: Dict[str, Any]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'alert_type': self.alert_type.value,
            'priority': self.priority.value,
            'title': self.title,
            'message': self.message,
            'action_required': self.action_required,
            'expiry_time': self.expiry_time.isoformat() if self.expiry_time else None,
            'metadata': self.metadata,
            'timestamp': self.timestamp.isoformat()
        }


class ProactiveTradingAssistant:
    """
    Proactive trading assistant that monitors market conditions and user behavior
    to provide timely alerts, briefings, and protective measures
    """
    
    def __init__(self, market_engine, ai_engine):
        self.logger = logging.getLogger(__name__)
        self.market_engine = market_engine
        self.ai_engine = ai_engine
        
        # Alert management
        self.active_alerts: List[ProactiveAlert] = []
        self.alert_callbacks: List[Callable] = []
        
        # Scheduling
        self.is_running = False
        self.morning_briefing_time = time(8, 30)  # 8:30 AM
        self.last_briefing_date = None
        
        # Market protection thresholds
        self.vix_protection_threshold = 30.0
        self.market_drop_threshold = -2.0  # 2% drop
        self.volume_spike_threshold = 2.0  # 2x normal volume
        
        # Opportunity detection settings
        self.min_signal_strength = 4  # 4+ star signals only
        self.opportunity_cooldown = 300  # 5 minutes between similar alerts
        self.last_opportunity_alerts = {}
        
        self.logger.info("🤖 Proactive Trading Assistant initialized")
    
    async def start_monitoring(self):
        """Start proactive monitoring and alert system"""
        if self.is_running:
            self.logger.warning("Proactive assistant already running")
            return
        
        self.is_running = True
        self.logger.info("🚀 Starting proactive trading assistant monitoring...")
        
        try:
            # Start monitoring tasks
            tasks = [
                self._morning_briefing_scheduler(),
                self._opportunity_monitor(),
                self._market_protection_monitor(),
                self._alert_cleanup_scheduler()
            ]
            
            await asyncio.gather(*tasks)
            
        except Exception as e:
            self.logger.error(f"Error in proactive monitoring: {e}")
        finally:
            self.is_running = False
    
    def stop_monitoring(self):
        """Stop proactive monitoring"""
        self.is_running = False
        self.logger.info("🛑 Stopping proactive trading assistant")
    
    async def _morning_briefing_scheduler(self):
        """Schedule and generate morning briefings"""
        while self.is_running:
            try:
                current_time = datetime.now().time()
                current_date = datetime.now().date()
                
                # Check if it's time for morning briefing
                if (current_time >= self.morning_briefing_time and 
                    self.last_briefing_date != current_date and
                    self._is_trading_day()):
                    
                    await self._generate_morning_briefing()
                    self.last_briefing_date = current_date
                
                # Sleep until next check (every 5 minutes)
                await asyncio.sleep(300)
                
            except Exception as e:
                self.logger.error(f"Error in morning briefing scheduler: {e}")
                await asyncio.sleep(60)
    
    async def _generate_morning_briefing(self):
        """Generate comprehensive morning briefing"""
        try:
            self.logger.info("📰 Generating morning briefing...")
            
            # Get market context
            market_context = await self.market_engine.get_market_context()
            
            # Get top TTM signals
            top_signals = self.market_engine.get_live_ttm_signals(min_strength=4)
            
            # Get sector performance
            sector_performance = await self.market_engine.get_sector_performance()
            
            # Generate briefing content
            briefing = await self._create_briefing_content(
                market_context, top_signals, sector_performance
            )
            
            # Create alert
            alert = ProactiveAlert(
                alert_type=AlertType.MORNING_BRIEFING,
                priority=AlertPriority.MEDIUM,
                title="🌅 Daily Market Briefing",
                message=briefing,
                action_required=False,
                expiry_time=datetime.now() + timedelta(hours=8),
                metadata={
                    'signals_count': len(top_signals),
                    'market_regime': market_context.get('market_regime', 'unknown')
                },
                timestamp=datetime.now()
            )
            
            await self._send_alert(alert)
            
        except Exception as e:
            self.logger.error(f"Error generating morning briefing: {e}")
    
    async def _create_briefing_content(self, market_context: Dict, 
                                     signals: List[Dict], 
                                     sectors: Dict[str, float]) -> str:
        """Create morning briefing content"""
        briefing = "☀️ **Good morning! Here's your daily market briefing:**\n\n"
        
        # Market overview
        if market_context:
            vix_level = market_context.get('vix_level', 20)
            market_sentiment = market_context.get('market_sentiment', 'neutral')
            risk_level = market_context.get('risk_level', 'moderate')
            
            briefing += f"📊 **Market Overview:**\n"
            briefing += f"• VIX Level: {vix_level:.1f} ({risk_level} risk environment)\n"
            briefing += f"• Market Sentiment: {market_sentiment.title()}\n"
            briefing += f"• Trading Conditions: {'Favorable' if vix_level < 20 else 'Cautious'}\n\n"
        
        # Top opportunities
        if signals:
            briefing += f"🎯 **Top Opportunities ({len(signals)} signals):**\n"
            for signal in signals[:3]:  # Top 3 signals
                stars = "⭐" * signal.get('signal_strength', 1)
                briefing += f"• **{signal['symbol']}** {stars} ${signal['current_price']:.2f} {signal['direction'].upper()}\n"
            briefing += "\n"
        
        # Sector rotation
        if sectors:
            sorted_sectors = sorted(sectors.items(), key=lambda x: x[1], reverse=True)
            top_sector = sorted_sectors[0] if sorted_sectors else None
            bottom_sector = sorted_sectors[-1] if sorted_sectors else None
            
            briefing += f"🔄 **Sector Rotation:**\n"
            if top_sector:
                briefing += f"• Leading: {top_sector[0]} (+{top_sector[1]:.1f}%)\n"
            if bottom_sector:
                briefing += f"• Lagging: {bottom_sector[0]} ({bottom_sector[1]:+.1f}%)\n"
            briefing += "\n"
        
        # Daily focus
        briefing += f"🎯 **Today's Focus:**\n"
        briefing += f"• Monitor TTM Squeeze signals for high-probability setups\n"
        briefing += f"• Watch for earnings announcements and volatility spikes\n"
        briefing += f"• Maintain disciplined risk management\n\n"
        
        briefing += f"💡 **Remember:** Trade your plan, not your emotions. Good luck today!"
        
        return briefing
    
    async def _opportunity_monitor(self):
        """Monitor for real-time trading opportunities"""
        while self.is_running:
            try:
                # Get current high-strength signals
                signals = self.market_engine.get_live_ttm_signals(min_strength=self.min_signal_strength)
                
                for signal in signals:
                    symbol = signal['symbol']
                    
                    # Check cooldown
                    if self._is_opportunity_on_cooldown(symbol):
                        continue
                    
                    # Check if this is a new high-priority opportunity
                    if signal['signal_strength'] >= 5 or signal['confidence_score'] >= 0.9:
                        await self._send_opportunity_alert(signal)
                        self.last_opportunity_alerts[symbol] = datetime.now()
                
                # Sleep between checks
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Error in opportunity monitor: {e}")
                await asyncio.sleep(30)
    
    def _is_opportunity_on_cooldown(self, symbol: str) -> bool:
        """Check if opportunity alert is on cooldown"""
        if symbol not in self.last_opportunity_alerts:
            return False
        
        last_alert = self.last_opportunity_alerts[symbol]
        return (datetime.now() - last_alert).seconds < self.opportunity_cooldown
    
    async def _send_opportunity_alert(self, signal: Dict[str, Any]):
        """Send time-sensitive opportunity alert"""
        try:
            stars = "⭐" * signal.get('signal_strength', 1)
            direction_emoji = "🚀" if signal['direction'] == 'long' else "📉"
            
            message = f"{direction_emoji} **{signal['symbol']} TTM SQUEEZE FIRED!** {stars}\n\n"
            message += f"💰 **Price:** ${signal['current_price']:.2f}\n"
            message += f"📈 **Direction:** {signal['direction'].upper()}\n"
            message += f"🎯 **Confidence:** {signal['confidence_score']*100:.0f}%\n\n"
            message += f"💡 **Analysis:** {signal['reasoning']}\n\n"
            message += f"⚡ **Action:** Consider this high-probability setup!"
            
            alert = ProactiveAlert(
                alert_type=AlertType.TIME_SENSITIVE_SETUP,
                priority=AlertPriority.HIGH,
                title=f"🎯 {signal['symbol']} High-Probability Setup",
                message=message,
                action_required=True,
                expiry_time=datetime.now() + timedelta(minutes=30),
                metadata={
                    'symbol': signal['symbol'],
                    'signal_strength': signal['signal_strength'],
                    'direction': signal['direction']
                },
                timestamp=datetime.now()
            )
            
            await self._send_alert(alert)
            
        except Exception as e:
            self.logger.error(f"Error sending opportunity alert: {e}")
    
    async def _market_protection_monitor(self):
        """Monitor market conditions for protective measures"""
        while self.is_running:
            try:
                # Get market context
                market_context = await self.market_engine.get_market_context()
                
                if market_context:
                    await self._check_vix_protection(market_context)
                    await self._check_market_drop_protection(market_context)
                
                # Sleep between checks
                await asyncio.sleep(120)  # Check every 2 minutes
                
            except Exception as e:
                self.logger.error(f"Error in market protection monitor: {e}")
                await asyncio.sleep(60)

    async def _check_vix_protection(self, market_context: Dict[str, Any]):
        """Check VIX levels for market protection alerts"""
        try:
            vix_level = market_context.get('vix_level', 20)

            if vix_level >= self.vix_protection_threshold:
                # Check if we already sent this alert recently
                if not self._has_recent_alert(AlertType.MARKET_PROTECTION, 'vix_spike'):
                    message = f"🚨 **MARKET PROTECTION ALERT**\n\n"
                    message += f"📊 **VIX Spike Detected:** {vix_level:.1f}\n"
                    message += f"⚠️ **Risk Level:** ELEVATED\n\n"
                    message += f"🛡️ **Recommended Actions:**\n"
                    message += f"• Reduce position sizes\n"
                    message += f"• Tighten stop losses\n"
                    message += f"• Avoid new long positions\n"
                    message += f"• Consider defensive positions\n\n"
                    message += f"💡 **Remember:** High VIX = High volatility. Trade smaller and safer!"

                    alert = ProactiveAlert(
                        alert_type=AlertType.MARKET_PROTECTION,
                        priority=AlertPriority.CRITICAL,
                        title="🚨 VIX Spike - Market Protection",
                        message=message,
                        action_required=True,
                        expiry_time=datetime.now() + timedelta(hours=2),
                        metadata={'vix_level': vix_level, 'protection_type': 'vix_spike'},
                        timestamp=datetime.now()
                    )

                    await self._send_alert(alert)

        except Exception as e:
            self.logger.error(f"Error checking VIX protection: {e}")

    async def _check_market_drop_protection(self, market_context: Dict[str, Any]):
        """Check for significant market drops"""
        try:
            # This would need market data to calculate daily change
            # For now, we'll use a placeholder implementation

            # Get SPY data to check market drop
            spy_data = await self.market_engine.market_data.get_real_time_quote('SPY')
            if spy_data:
                # Calculate daily change (placeholder - would need opening price)
                daily_change = -1.5  # Placeholder for demonstration

                if daily_change <= self.market_drop_threshold:
                    if not self._has_recent_alert(AlertType.MARKET_PROTECTION, 'market_drop'):
                        message = f"📉 **MARKET DROP ALERT**\n\n"
                        message += f"📊 **Market Down:** {daily_change:.1f}%\n"
                        message += f"⚠️ **Trend:** Bearish pressure\n\n"
                        message += f"🛡️ **Protective Measures:**\n"
                        message += f"• Review open positions\n"
                        message += f"• Consider taking profits\n"
                        message += f"• Avoid catching falling knives\n"
                        message += f"• Wait for stabilization\n\n"
                        message += f"💡 **Strategy:** Let the dust settle before making new trades"

                        alert = ProactiveAlert(
                            alert_type=AlertType.MARKET_PROTECTION,
                            priority=AlertPriority.HIGH,
                            title="📉 Market Drop Protection",
                            message=message,
                            action_required=True,
                            expiry_time=datetime.now() + timedelta(hours=1),
                            metadata={'daily_change': daily_change, 'protection_type': 'market_drop'},
                            timestamp=datetime.now()
                        )

                        await self._send_alert(alert)

        except Exception as e:
            self.logger.error(f"Error checking market drop protection: {e}")

    def _has_recent_alert(self, alert_type: AlertType, metadata_key: str) -> bool:
        """Check if similar alert was sent recently"""
        cutoff_time = datetime.now() - timedelta(hours=1)

        for alert in self.active_alerts:
            if (alert.alert_type == alert_type and
                alert.timestamp > cutoff_time and
                metadata_key in alert.metadata.get('protection_type', '')):
                return True

        return False

    async def _alert_cleanup_scheduler(self):
        """Clean up expired alerts"""
        while self.is_running:
            try:
                current_time = datetime.now()

                # Remove expired alerts
                self.active_alerts = [
                    alert for alert in self.active_alerts
                    if not alert.expiry_time or alert.expiry_time > current_time
                ]

                # Sleep for 10 minutes between cleanups
                await asyncio.sleep(600)

            except Exception as e:
                self.logger.error(f"Error in alert cleanup: {e}")
                await asyncio.sleep(300)

    async def _send_alert(self, alert: ProactiveAlert):
        """Send alert to all registered callbacks"""
        try:
            # Add to active alerts
            self.active_alerts.append(alert)

            # Log the alert
            priority_emoji = {
                AlertPriority.LOW: "ℹ️",
                AlertPriority.MEDIUM: "⚠️",
                AlertPriority.HIGH: "🚨",
                AlertPriority.CRITICAL: "🔥"
            }

            emoji = priority_emoji.get(alert.priority, "📢")
            self.logger.info(f"{emoji} Proactive Alert: {alert.title}")

            # Send to all callbacks
            for callback in self.alert_callbacks:
                try:
                    await callback(alert)
                except Exception as e:
                    self.logger.error(f"Error in alert callback: {e}")

        except Exception as e:
            self.logger.error(f"Error sending alert: {e}")

    def register_alert_callback(self, callback: Callable):
        """Register callback for proactive alerts"""
        self.alert_callbacks.append(callback)
        self.logger.info("Alert callback registered")

    def get_active_alerts(self, alert_type: AlertType = None) -> List[ProactiveAlert]:
        """Get current active alerts"""
        if alert_type:
            return [alert for alert in self.active_alerts if alert.alert_type == alert_type]
        return self.active_alerts.copy()

    def dismiss_alert(self, alert_index: int) -> bool:
        """Dismiss an active alert"""
        try:
            if 0 <= alert_index < len(self.active_alerts):
                dismissed = self.active_alerts.pop(alert_index)
                self.logger.info(f"Alert dismissed: {dismissed.title}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error dismissing alert: {e}")
            return False

    def _is_trading_day(self) -> bool:
        """Check if today is a trading day (simple weekday check)"""
        return datetime.now().weekday() < 5  # Monday = 0, Friday = 4

    async def send_custom_alert(self, title: str, message: str,
                              priority: AlertPriority = AlertPriority.MEDIUM,
                              action_required: bool = False,
                              expiry_hours: int = 1) -> bool:
        """Send custom alert"""
        try:
            alert = ProactiveAlert(
                alert_type=AlertType.OPPORTUNITY_NOTIFICATION,
                priority=priority,
                title=title,
                message=message,
                action_required=action_required,
                expiry_time=datetime.now() + timedelta(hours=expiry_hours),
                metadata={'custom': True},
                timestamp=datetime.now()
            )

            await self._send_alert(alert)
            return True

        except Exception as e:
            self.logger.error(f"Error sending custom alert: {e}")
            return False

    def get_assistant_status(self) -> Dict[str, Any]:
        """Get proactive assistant status"""
        return {
            'is_running': self.is_running,
            'active_alerts_count': len(self.active_alerts),
            'last_briefing_date': self.last_briefing_date.isoformat() if self.last_briefing_date else None,
            'morning_briefing_time': self.morning_briefing_time.strftime('%H:%M'),
            'protection_thresholds': {
                'vix_threshold': self.vix_protection_threshold,
                'market_drop_threshold': self.market_drop_threshold
            },
            'opportunity_settings': {
                'min_signal_strength': self.min_signal_strength,
                'cooldown_seconds': self.opportunity_cooldown
            }
        }
