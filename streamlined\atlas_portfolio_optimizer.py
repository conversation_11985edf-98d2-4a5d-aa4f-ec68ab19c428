"""
A.T.L.A.S Deep Portfolio Optimization Module
MLP-based portfolio optimization with weekly auto-rebalancing and risk management
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import json
import pickle
import os
from scipy.optimize import minimize
from collections import defaultdict

# Try to import ML libraries, fallback to simple optimization if not available
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import Dense, Dropout
    from sklearn.preprocessing import StandardScaler
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

from config import settings
from atlas_performance_optimizer import performance_optimizer


@dataclass
class PortfolioAsset:
    """Portfolio asset with features"""
    symbol: str
    current_weight: float
    target_weight: float
    expected_return: float
    volatility: float
    beta: float
    market_cap: float
    sector: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'current_weight': self.current_weight,
            'target_weight': self.target_weight,
            'expected_return': self.expected_return,
            'volatility': self.volatility,
            'beta': self.beta,
            'market_cap': self.market_cap,
            'sector': self.sector
        }


@dataclass
class PortfolioOptimizationResult:
    """Portfolio optimization result"""
    assets: List[PortfolioAsset]
    expected_return: float
    expected_volatility: float
    sharpe_ratio: float
    max_drawdown: float
    turnover: float
    rebalancing_cost: float
    optimization_method: str
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'assets': [asset.to_dict() for asset in self.assets],
            'expected_return': self.expected_return,
            'expected_volatility': self.expected_volatility,
            'sharpe_ratio': self.sharpe_ratio,
            'max_drawdown': self.max_drawdown,
            'turnover': self.turnover,
            'rebalancing_cost': self.rebalancing_cost,
            'optimization_method': self.optimization_method,
            'timestamp': self.timestamp.isoformat()
        }


class DeepPortfolioOptimizer:
    """
    Deep Portfolio Optimization with MLP return prediction and mean-variance optimization
    """
    
    def __init__(self, market_engine, trading_engine):
        self.logger = logging.getLogger(__name__)
        self.market_engine = market_engine
        self.trading_engine = trading_engine
        
        # Portfolio universe (25 high-liquidity assets)
        self.universe = [
            # Large Cap Stocks
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX', 'AMD', 'CRM',
            # Financial Sector
            'JPM', 'BAC', 'GS', 'MS', 'V', 'MA',
            # ETFs for diversification
            'SPY', 'QQQ', 'IWM', 'VTI', 'VEA', 'VWO', 'AGG', 'TLT', 'GLD'
        ]
        
        # ML Model for return prediction
        self.return_predictor = None
        self.feature_scaler = StandardScaler() if ML_AVAILABLE else None
        self.feature_columns = [
            'momentum_1m', 'momentum_3m', 'volatility', 'rsi', 'pe_ratio',
            'market_beta', 'volume_ratio', 'sector_momentum'
        ]
        
        # Portfolio optimization parameters
        self.risk_aversion = 2.0  # Risk aversion parameter
        self.target_return = 0.15  # 15% annual target return
        self.max_weight = 0.15  # 15% maximum weight per asset
        self.min_weight = 0.01  # 1% minimum weight per asset
        self.rebalancing_threshold = 0.05  # 5% drift threshold
        self.transaction_cost = 0.001  # 0.1% transaction cost
        
        # Current portfolio state
        self.current_portfolio = {}
        self.last_rebalance_date = None
        self.rebalance_frequency_days = 7  # Weekly rebalancing
        
        # Performance tracking
        self.optimization_history = []
        self.portfolio_returns = []
        
        # Model persistence
        self.model_dir = "models"
        self.predictor_path = os.path.join(self.model_dir, "portfolio_return_predictor.h5")
        self.scaler_path = os.path.join(self.model_dir, "portfolio_scaler.pkl")
        
        # Initialize
        self._ensure_model_directory()
        if ML_AVAILABLE:
            self._initialize_return_predictor()
        
        self.logger.info(f"📊 Deep Portfolio Optimizer initialized (ML: {'✅' if ML_AVAILABLE else '❌'})")
    
    def _ensure_model_directory(self):
        """Ensure model directory exists"""
        if not os.path.exists(self.model_dir):
            os.makedirs(self.model_dir)
    
    def _initialize_return_predictor(self):
        """Initialize MLP model for return prediction"""
        try:
            if os.path.exists(self.predictor_path) and os.path.exists(self.scaler_path):
                # Load existing model
                self.return_predictor = tf.keras.models.load_model(self.predictor_path)
                with open(self.scaler_path, 'rb') as f:
                    self.feature_scaler = pickle.load(f)
                self.logger.info("✅ Loaded existing return predictor model")
            else:
                # Create new model
                self.return_predictor = Sequential([
                    Dense(64, activation='relu', input_shape=(len(self.feature_columns),)),
                    Dropout(0.3),
                    Dense(32, activation='relu'),
                    Dropout(0.2),
                    Dense(16, activation='relu'),
                    Dense(1, activation='linear')  # Linear output for return prediction
                ])
                
                self.return_predictor.compile(
                    optimizer='adam',
                    loss='mse',
                    metrics=['mae']
                )
                
                self.logger.info("🆕 Created new return predictor model")
                
        except Exception as e:
            self.logger.error(f"Error initializing return predictor: {e}")
            self.return_predictor = None
    
    @performance_optimizer.performance_monitor("optimize_portfolio")
    async def optimize_portfolio(self, force_rebalance: bool = False) -> Optional[PortfolioOptimizationResult]:
        """Optimize portfolio weights using ML predictions and mean-variance optimization"""
        try:
            # Check if rebalancing is needed
            if not force_rebalance and not await self._should_rebalance():
                self.logger.info("Portfolio rebalancing not needed")
                return None
            
            self.logger.info("🔄 Starting portfolio optimization...")
            
            # Get current portfolio state
            current_weights = await self._get_current_portfolio_weights()
            
            # Prepare features for all assets
            features_data = await self._prepare_portfolio_features()
            
            if not features_data:
                self.logger.error("Failed to prepare portfolio features")
                return None
            
            # Predict returns using ML model
            predicted_returns = await self._predict_asset_returns(features_data)
            
            # Calculate covariance matrix
            covariance_matrix = await self._calculate_covariance_matrix()
            
            # Optimize portfolio weights
            optimal_weights = self._optimize_weights(
                predicted_returns, covariance_matrix, current_weights
            )
            
            if not optimal_weights:
                self.logger.error("Portfolio optimization failed")
                return None
            
            # Create portfolio assets with new weights
            assets = []
            for i, symbol in enumerate(self.universe):
                current_weight = current_weights.get(symbol, 0.0)
                target_weight = optimal_weights[i]
                
                assets.append(PortfolioAsset(
                    symbol=symbol,
                    current_weight=current_weight,
                    target_weight=target_weight,
                    expected_return=predicted_returns[i],
                    volatility=np.sqrt(covariance_matrix[i, i]),
                    beta=await self._calculate_beta(symbol),
                    market_cap=await self._get_market_cap(symbol),
                    sector=await self._get_sector(symbol)
                ))
            
            # Calculate portfolio metrics
            portfolio_return = np.sum(predicted_returns * optimal_weights)
            portfolio_volatility = np.sqrt(
                np.dot(optimal_weights, np.dot(covariance_matrix, optimal_weights))
            )
            sharpe_ratio = portfolio_return / max(portfolio_volatility, 0.01)
            
            # Calculate turnover
            turnover = self._calculate_turnover(current_weights, optimal_weights)
            
            # Calculate rebalancing cost
            rebalancing_cost = turnover * self.transaction_cost
            
            result = PortfolioOptimizationResult(
                assets=assets,
                expected_return=portfolio_return,
                expected_volatility=portfolio_volatility,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=0.0,  # Would need historical simulation
                turnover=turnover,
                rebalancing_cost=rebalancing_cost,
                optimization_method='ml_mean_variance',
                timestamp=datetime.now()
            )
            
            # Store optimization result
            self.optimization_history.append(result)
            
            self.logger.info(f"✅ Portfolio optimized: Return={portfolio_return:.3f}, Volatility={portfolio_volatility:.3f}, Sharpe={sharpe_ratio:.3f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error optimizing portfolio: {e}")
            return None
    
    async def _prepare_portfolio_features(self) -> Optional[pd.DataFrame]:
        """Prepare features for all assets in universe"""
        try:
            features_data = []
            
            for symbol in self.universe:
                try:
                    # Get historical data
                    historical_data = await self.market_engine.market_data.get_historical_data(
                        symbol, limit=100
                    )
                    
                    if not historical_data or len(historical_data) < 50:
                        continue
                    
                    # Convert to DataFrame
                    df = pd.DataFrame([{
                        'close': bar.close,
                        'volume': bar.volume,
                        'timestamp': bar.timestamp
                    } for bar in historical_data])
                    
                    # Calculate features
                    features = self._calculate_asset_features(df, symbol)
                    features['symbol'] = symbol
                    features_data.append(features)
                    
                except Exception as e:
                    self.logger.error(f"Error preparing features for {symbol}: {e}")
                    continue
            
            if not features_data:
                return None
            
            return pd.DataFrame(features_data)
            
        except Exception as e:
            self.logger.error(f"Error preparing portfolio features: {e}")
            return None
    
    def _calculate_asset_features(self, df: pd.DataFrame, symbol: str) -> Dict[str, float]:
        """Calculate features for a single asset"""
        try:
            # Price momentum
            momentum_1m = (df['close'].iloc[-1] / df['close'].iloc[-20] - 1) if len(df) >= 20 else 0
            momentum_3m = (df['close'].iloc[-1] / df['close'].iloc[-60] - 1) if len(df) >= 60 else 0
            
            # Volatility
            returns = df['close'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(252)  # Annualized
            
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs.iloc[-1])) if not pd.isna(rs.iloc[-1]) else 50
            
            # Volume ratio
            avg_volume = df['volume'].rolling(20).mean().iloc[-1]
            current_volume = df['volume'].iloc[-1]
            volume_ratio = current_volume / max(avg_volume, 1)
            
            return {
                'momentum_1m': momentum_1m,
                'momentum_3m': momentum_3m,
                'volatility': volatility,
                'rsi': rsi / 100.0,  # Normalize to 0-1
                'pe_ratio': 0.5,  # Placeholder - would need fundamental data
                'market_beta': 1.0,  # Placeholder - would calculate vs market
                'volume_ratio': min(volume_ratio, 5.0),  # Cap at 5x
                'sector_momentum': 0.0  # Placeholder - would need sector data
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating features for {symbol}: {e}")
            return {col: 0.0 for col in self.feature_columns}
    
    async def _predict_asset_returns(self, features_data: pd.DataFrame) -> np.ndarray:
        """Predict asset returns using ML model"""
        try:
            if not ML_AVAILABLE or not self.return_predictor:
                # Fallback to simple momentum-based predictions
                return self._simple_return_prediction(features_data)
            
            # Prepare features for ML model
            feature_matrix = features_data[self.feature_columns].values
            
            # Normalize features
            normalized_features = self.feature_scaler.fit_transform(feature_matrix)
            
            # Predict returns
            predictions = self.return_predictor.predict(normalized_features, verbose=0)
            
            return predictions.flatten()
            
        except Exception as e:
            self.logger.error(f"Error predicting asset returns: {e}")
            return self._simple_return_prediction(features_data)
    
    def _simple_return_prediction(self, features_data: pd.DataFrame) -> np.ndarray:
        """Simple momentum-based return prediction as fallback"""
        try:
            # Use momentum and mean reversion
            momentum_1m = features_data['momentum_1m'].values
            momentum_3m = features_data['momentum_3m'].values
            volatility = features_data['volatility'].values
            
            # Combine momentum signals with volatility adjustment
            predicted_returns = (momentum_1m * 0.3 + momentum_3m * 0.2) / (1 + volatility)
            
            # Add base return assumption
            predicted_returns += 0.08  # 8% base return assumption
            
            return predicted_returns
            
        except Exception as e:
            self.logger.error(f"Error in simple return prediction: {e}")
            return np.full(len(features_data), 0.08)  # Default 8% return
    
    async def _calculate_covariance_matrix(self) -> np.ndarray:
        """Calculate covariance matrix for portfolio optimization"""
        try:
            # Get historical returns for all assets
            returns_data = []
            
            for symbol in self.universe:
                try:
                    historical_data = await self.market_engine.market_data.get_historical_data(
                        symbol, limit=252  # 1 year of data
                    )
                    
                    if historical_data and len(historical_data) > 50:
                        prices = [bar.close for bar in historical_data]
                        returns = [prices[i] / prices[i-1] - 1 for i in range(1, len(prices))]
                        returns_data.append(returns[-100:])  # Last 100 returns
                    else:
                        # Default returns if no data
                        returns_data.append([0.001] * 100)  # 0.1% daily return
                        
                except Exception as e:
                    returns_data.append([0.001] * 100)  # Default fallback
            
            # Convert to numpy array and calculate covariance
            returns_matrix = np.array(returns_data).T  # Transpose for proper shape
            covariance_matrix = np.cov(returns_matrix.T) * 252  # Annualized
            
            # Apply shrinkage to improve stability
            covariance_matrix = self._shrink_covariance_matrix(covariance_matrix)
            
            return covariance_matrix
            
        except Exception as e:
            self.logger.error(f"Error calculating covariance matrix: {e}")
            # Return identity matrix as fallback
            n_assets = len(self.universe)
            return np.eye(n_assets) * 0.04  # 20% volatility assumption

    def _shrink_covariance_matrix(self, cov_matrix: np.ndarray) -> np.ndarray:
        """Apply shrinkage to covariance matrix for stability"""
        try:
            # Ledoit-Wolf shrinkage (simplified)
            n = cov_matrix.shape[0]

            # Target matrix (identity scaled by average variance)
            avg_variance = np.trace(cov_matrix) / n
            target = np.eye(n) * avg_variance

            # Shrinkage intensity (simplified)
            shrinkage = 0.2  # 20% shrinkage

            # Apply shrinkage
            shrunk_cov = (1 - shrinkage) * cov_matrix + shrinkage * target

            return shrunk_cov

        except Exception as e:
            self.logger.error(f"Error in covariance shrinkage: {e}")
            return cov_matrix

    def _optimize_weights(self, expected_returns: np.ndarray,
                         covariance_matrix: np.ndarray,
                         current_weights: Dict[str, float]) -> Optional[np.ndarray]:
        """Optimize portfolio weights using mean-variance optimization"""
        try:
            n_assets = len(self.universe)

            # Convert current weights to array
            current_weight_array = np.array([current_weights.get(symbol, 0.0) for symbol in self.universe])

            # Objective function: minimize risk - risk_aversion * return + transaction_costs
            def objective(weights):
                portfolio_return = np.dot(weights, expected_returns)
                portfolio_variance = np.dot(weights, np.dot(covariance_matrix, weights))

                # Transaction costs
                turnover = np.sum(np.abs(weights - current_weight_array))
                transaction_costs = turnover * self.transaction_cost

                # Mean-variance objective with transaction costs
                return portfolio_variance - self.risk_aversion * portfolio_return + transaction_costs

            # Constraints
            constraints = [
                {'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0},  # Weights sum to 1
            ]

            # Bounds for each weight
            bounds = [(self.min_weight, self.max_weight) for _ in range(n_assets)]

            # Initial guess (equal weights)
            x0 = np.full(n_assets, 1.0 / n_assets)

            # Optimize
            result = minimize(
                objective,
                x0,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': 1000}
            )

            if result.success:
                optimal_weights = result.x

                # Validate weights
                if abs(np.sum(optimal_weights) - 1.0) > 0.01:
                    self.logger.warning("Weight constraint violation, normalizing")
                    optimal_weights = optimal_weights / np.sum(optimal_weights)

                return optimal_weights
            else:
                self.logger.error(f"Optimization failed: {result.message}")
                return None

        except Exception as e:
            self.logger.error(f"Error in weight optimization: {e}")
            return None

    async def execute_rebalancing(self, optimization_result: PortfolioOptimizationResult) -> Dict[str, Any]:
        """Execute portfolio rebalancing based on optimization result"""
        try:
            self.logger.info("🔄 Executing portfolio rebalancing...")

            # Get current account value
            account_info = await self.trading_engine.get_account_info()
            account_value = float(account_info.get('equity', 100000))

            execution_results = []
            total_trades = 0
            successful_trades = 0

            for asset in optimization_result.assets:
                try:
                    # Calculate position changes
                    current_value = account_value * asset.current_weight
                    target_value = account_value * asset.target_weight
                    position_change = target_value - current_value

                    # Skip small changes
                    if abs(position_change) < account_value * 0.005:  # Less than 0.5% of portfolio
                        continue

                    # Get current price
                    quote = await self.market_engine.market_data.get_real_time_quote(asset.symbol)
                    if not quote:
                        continue

                    # Calculate shares to trade
                    shares_to_trade = int(abs(position_change) / quote.price)
                    if shares_to_trade == 0:
                        continue

                    # Determine side
                    side = 'buy' if position_change > 0 else 'sell'

                    # Place order
                    order_result = await self.trading_engine.place_order(
                        symbol=asset.symbol,
                        qty=shares_to_trade,
                        side=side,
                        type='market',
                        time_in_force='day',
                        source='portfolio_optimizer'
                    )

                    total_trades += 1

                    if order_result and order_result.get('success'):
                        successful_trades += 1
                        execution_results.append({
                            'symbol': asset.symbol,
                            'side': side,
                            'quantity': shares_to_trade,
                            'target_weight': asset.target_weight,
                            'success': True,
                            'order_id': order_result.get('order_id')
                        })

                        self.logger.info(f"✅ Rebalanced {asset.symbol}: {side} {shares_to_trade} shares")
                    else:
                        execution_results.append({
                            'symbol': asset.symbol,
                            'side': side,
                            'quantity': shares_to_trade,
                            'target_weight': asset.target_weight,
                            'success': False,
                            'error': order_result.get('error', 'Unknown error')
                        })

                        self.logger.error(f"❌ Failed to rebalance {asset.symbol}")

                except Exception as e:
                    self.logger.error(f"Error rebalancing {asset.symbol}: {e}")
                    continue

            # Update last rebalance date
            self.last_rebalance_date = datetime.now()

            # Update current portfolio weights (simplified)
            for asset in optimization_result.assets:
                if any(r['symbol'] == asset.symbol and r['success'] for r in execution_results):
                    self.current_portfolio[asset.symbol] = asset.target_weight

            result = {
                'success': successful_trades > 0,
                'total_trades': total_trades,
                'successful_trades': successful_trades,
                'success_rate': successful_trades / max(total_trades, 1) * 100,
                'execution_results': execution_results,
                'rebalancing_cost': optimization_result.rebalancing_cost,
                'timestamp': datetime.now().isoformat()
            }

            self.logger.info(f"🔄 Rebalancing completed: {successful_trades}/{total_trades} trades successful")

            return result

        except Exception as e:
            self.logger.error(f"Error executing rebalancing: {e}")
            return {'success': False, 'error': str(e)}

    async def _should_rebalance(self) -> bool:
        """Check if portfolio should be rebalanced"""
        try:
            # Check time since last rebalance
            if self.last_rebalance_date:
                days_since_rebalance = (datetime.now() - self.last_rebalance_date).days
                if days_since_rebalance < self.rebalance_frequency_days:
                    return False

            # Check portfolio drift
            current_weights = await self._get_current_portfolio_weights()

            if not current_weights:
                return True  # First time setup

            # Calculate maximum drift
            max_drift = 0.0
            for symbol in self.universe:
                current_weight = current_weights.get(symbol, 0.0)
                target_weight = self.current_portfolio.get(symbol, 1.0 / len(self.universe))
                drift = abs(current_weight - target_weight)
                max_drift = max(max_drift, drift)

            return max_drift > self.rebalancing_threshold

        except Exception as e:
            self.logger.error(f"Error checking rebalance condition: {e}")
            return False

    async def _get_current_portfolio_weights(self) -> Dict[str, float]:
        """Get current portfolio weights"""
        try:
            # Get current positions
            positions = await self.trading_engine.get_positions()

            if not positions:
                return {}

            # Calculate total portfolio value
            total_value = 0.0
            position_values = {}

            for position in positions:
                symbol = position.get('symbol')
                quantity = float(position.get('qty', 0))

                if symbol in self.universe and quantity > 0:
                    # Get current price
                    quote = await self.market_engine.market_data.get_real_time_quote(symbol)
                    if quote:
                        value = quantity * quote.price
                        position_values[symbol] = value
                        total_value += value

            # Calculate weights
            weights = {}
            for symbol, value in position_values.items():
                weights[symbol] = value / max(total_value, 1)

            return weights

        except Exception as e:
            self.logger.error(f"Error getting current portfolio weights: {e}")
            return {}

    def _calculate_turnover(self, current_weights: Dict[str, float],
                          optimal_weights: np.ndarray) -> float:
        """Calculate portfolio turnover"""
        try:
            turnover = 0.0

            for i, symbol in enumerate(self.universe):
                current_weight = current_weights.get(symbol, 0.0)
                target_weight = optimal_weights[i]
                turnover += abs(target_weight - current_weight)

            return turnover / 2.0  # Divide by 2 for one-way turnover

        except Exception as e:
            self.logger.error(f"Error calculating turnover: {e}")
            return 0.0

    async def _calculate_beta(self, symbol: str) -> float:
        """Calculate beta vs market (simplified)"""
        try:
            # This would typically calculate correlation with market index
            # For now, return default values based on asset type
            if symbol in ['SPY', 'VTI']:
                return 1.0  # Market beta
            elif symbol in ['QQQ', 'NVDA', 'TSLA']:
                return 1.5  # High beta tech
            elif symbol in ['AGG', 'TLT']:
                return 0.2  # Low beta bonds
            else:
                return 1.0  # Default

        except Exception as e:
            return 1.0

    async def _get_market_cap(self, symbol: str) -> float:
        """Get market capitalization (simplified)"""
        # This would typically come from fundamental data API
        large_caps = ['AAPL', 'MSFT', 'GOOGL', 'AMZN']
        if symbol in large_caps:
            return 2000000000000  # $2T
        else:
            return 500000000000   # $500B

    async def _get_sector(self, symbol: str) -> str:
        """Get sector classification (simplified)"""
        sector_mapping = {
            'AAPL': 'Technology', 'MSFT': 'Technology', 'GOOGL': 'Technology',
            'AMZN': 'Consumer Discretionary', 'TSLA': 'Consumer Discretionary',
            'JPM': 'Financials', 'BAC': 'Financials', 'GS': 'Financials',
            'SPY': 'ETF', 'QQQ': 'ETF', 'AGG': 'ETF'
        }
        return sector_mapping.get(symbol, 'Other')

    async def get_portfolio_status(self) -> Dict[str, Any]:
        """Get portfolio optimizer status"""
        try:
            current_weights = await self._get_current_portfolio_weights()

            return {
                'ml_available': ML_AVAILABLE,
                'return_predictor_loaded': self.return_predictor is not None,
                'universe_size': len(self.universe),
                'current_portfolio_size': len(current_weights),
                'last_rebalance_date': self.last_rebalance_date.isoformat() if self.last_rebalance_date else None,
                'optimization_history_size': len(self.optimization_history),
                'parameters': {
                    'risk_aversion': self.risk_aversion,
                    'target_return': self.target_return,
                    'max_weight': self.max_weight,
                    'min_weight': self.min_weight,
                    'rebalancing_threshold': self.rebalancing_threshold,
                    'transaction_cost': self.transaction_cost,
                    'rebalance_frequency_days': self.rebalance_frequency_days
                },
                'current_weights': current_weights,
                'should_rebalance': await self._should_rebalance()
            }

        except Exception as e:
            self.logger.error(f"Error getting portfolio status: {e}")
            return {'error': str(e)}
