"""
A.T.L.A.S Education Engine - Consolidated Educational and Memory System
Combines trading books RAG, memory system, options education, and learning features
"""

import logging
import sqlite3
import json
import hashlib
import numpy as np
import pickle
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import re

from config import settings
from models import (
    BookContent, EducationQuery, EducationResponse, ChatMessage,
    Quote, TechnicalIndicators
)

# Try to import vector database libraries
try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False


@dataclass
class MemoryEntry:
    """Memory system entry"""
    session_id: str
    memory_type: str  # 'conversation', 'preference', 'learning', 'performance'
    content: str
    metadata: Dict[str, Any]
    timestamp: datetime
    importance_score: float


@dataclass
class BookChunk:
    """Chunk of book content for RAG"""
    book_title: str
    chapter: str
    section: str
    content: str
    chunk_id: str
    embedding: Optional[np.ndarray] = None
    metadata: Dict[str, Any] = None


class TradingBooksRAG:
    """Advanced RAG system for trading books with vector search"""

    def __init__(self, db_path: str = "atlas_rag.db"):
        self.logger = logging.getLogger(__name__)
        self.db_path = db_path
        self.vector_db_path = "atlas_vectors.faiss"
        self.embeddings_path = "atlas_embeddings.pkl"

        # Initialize components
        self.encoder = None
        self.vector_index = None
        self.book_chunks: List[BookChunk] = []

        # Initialize database
        self._initialize_database()

        # Initialize vector components if available
        if SENTENCE_TRANSFORMERS_AVAILABLE and FAISS_AVAILABLE:
            self._initialize_vector_components()
        else:
            self.logger.warning("FAISS or SentenceTransformers not available. Using basic text search.")

        # Enhanced RAG with comprehensive trading literature
        self.enhanced_sources = {
            'infobooks_trading': [
                'Trading in the Zone by Mark Douglas',
                'Market Wizards by Jack Schwager',
                'The New Market Wizards by Jack Schwager',
                'Reminiscences of a Stock Operator by Edwin Lefèvre',
                'Technical Analysis of the Financial Markets by John Murphy',
                'Options as a Strategic Investment by Lawrence McMillan',
                'The Options Playbook by Brian Overby',
                'Quantitative Trading by Ernest Chan',
                'Algorithmic Trading by Ernest Chan',
                'The Intelligent Investor by Benjamin Graham'
            ],
            'internet_archive_trading': [
                'Advanced Options Trading Strategies',
                'Behavioral Finance and Trading Psychology',
                'Risk Management in Trading',
                'Quantitative Methods for Traders',
                'Day Trading and Swing Trading Techniques',
                'Technical Analysis Patterns and Indicators',
                'Options Greeks and Volatility Trading',
                'Portfolio Management and Asset Allocation'
            ]
        }

        # Initialize enhanced knowledge base
        self._initialize_enhanced_knowledge_base()

        self.logger.info("📚 A.T.L.A.S Education Engine initialized with enhanced RAG capabilities")

    def _initialize_database(self):
        """Initialize RAG database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Book chunks table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS book_chunks (
                    chunk_id TEXT PRIMARY KEY,
                    book_title TEXT,
                    chapter TEXT,
                    section TEXT,
                    content TEXT,
                    metadata TEXT,
                    created_at TEXT
                )
            ''')

            # Book metadata table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS book_metadata (
                    book_title TEXT PRIMARY KEY,
                    author TEXT,
                    category TEXT,
                    description TEXT,
                    total_chunks INTEGER,
                    added_at TEXT
                )
            ''')

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error initializing RAG database: {e}")

    def _initialize_enhanced_knowledge_base(self):
        """Initialize enhanced knowledge base with comprehensive trading literature"""
        try:
            # Check if enhanced content already exists
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM book_metadata WHERE category = 'enhanced_trading'")
            enhanced_count = cursor.fetchone()[0]

            conn.close()

            if enhanced_count > 0:
                self.logger.info(f"Enhanced knowledge base already initialized with {enhanced_count} books")
                return

            # Add comprehensive trading knowledge
            self._add_enhanced_trading_content()

            self.logger.info("✅ Enhanced knowledge base initialized with comprehensive trading literature")

        except Exception as e:
            self.logger.error(f"Error initializing enhanced knowledge base: {e}")

    def _add_enhanced_trading_content(self):
        """Add enhanced trading content from various sources"""
        try:
            # Add InfoBooks.org trading content (simulated)
            for book_title in self.enhanced_sources['infobooks_trading']:
                content = self._generate_enhanced_book_content(book_title)
                author = self._extract_author_from_title(book_title)

                self.add_book_content(
                    book_title=book_title,
                    author=author,
                    content=content,
                    category='enhanced_trading'
                )

            # Add Internet Archive trading content (simulated)
            for topic in self.enhanced_sources['internet_archive_trading']:
                content = self._generate_topic_content(topic)

                self.add_book_content(
                    book_title=f"Comprehensive Guide to {topic}",
                    author="Trading Experts",
                    content=content,
                    category='enhanced_trading'
                )

            self.logger.info("📚 Enhanced trading content added to knowledge base")

        except Exception as e:
            self.logger.error(f"Error adding enhanced trading content: {e}")

    def _generate_enhanced_book_content(self, book_title: str) -> str:
        """Generate enhanced book content based on title (simulated comprehensive content)"""
        try:
            # This would typically process actual PDFs from InfoBooks.org
            # For now, we'll generate comprehensive content based on the book title

            if "Trading in the Zone" in book_title:
                return """
                Chapter 1: The Psychology of Trading

                Trading psychology is the foundation of successful trading. Mark Douglas emphasizes that trading is 80% psychology and 20% methodology. The key psychological challenges traders face include:

                1. Fear of Loss: This prevents traders from taking necessary risks and leads to missed opportunities.
                2. Fear of Missing Out (FOMO): Causes traders to enter positions without proper analysis.
                3. Overconfidence: Leads to position sizing errors and inadequate risk management.
                4. Revenge Trading: Attempting to recover losses through increasingly risky trades.

                Chapter 2: Developing a Winning Mindset

                A winning mindset requires:
                - Accepting uncertainty as a fundamental aspect of trading
                - Understanding that each trade is just one in a series of trades
                - Focusing on process over outcomes
                - Maintaining emotional equilibrium regardless of individual trade results

                Chapter 3: Risk Management and Position Sizing

                Proper risk management involves:
                - Never risking more than 1-2% of capital per trade
                - Using stop losses on every trade
                - Position sizing based on volatility and account size
                - Diversification across uncorrelated strategies

                Chapter 4: The Importance of Consistency

                Consistency in trading comes from:
                - Following a well-defined trading plan
                - Maintaining detailed trading records
                - Regular performance analysis and improvement
                - Emotional discipline and self-control
                """

            elif "Market Wizards" in book_title:
                return """
                Chapter 1: Interviews with Top Traders

                Jack Schwager's interviews reveal common traits among successful traders:

                1. Risk Management: All successful traders emphasize cutting losses quickly
                2. Patience: Waiting for high-probability setups
                3. Discipline: Following their trading rules consistently
                4. Continuous Learning: Adapting to changing market conditions

                Chapter 2: Technical Analysis Masters

                Technical analysis principles from market wizards:
                - Trend following strategies work in trending markets
                - Support and resistance levels are crucial for entry and exit points
                - Volume analysis confirms price movements
                - Multiple timeframe analysis improves trade timing

                Chapter 3: Fundamental Analysis Experts

                Fundamental analysis insights:
                - Understanding macroeconomic factors
                - Company financial analysis for stock selection
                - Sector rotation strategies
                - Economic indicators and their market impact

                Chapter 4: Options and Derivatives Trading

                Advanced trading strategies:
                - Options strategies for income generation
                - Hedging techniques using derivatives
                - Volatility trading strategies
                - Risk management with options
                """

            elif "Options" in book_title:
                return """
                Chapter 1: Options Fundamentals

                Options basics every trader should know:

                1. Call Options: Right to buy at strike price
                2. Put Options: Right to sell at strike price
                3. The Greeks: Delta, Gamma, Theta, Vega, Rho
                4. Intrinsic vs Extrinsic Value

                Chapter 2: Basic Options Strategies

                Essential options strategies:
                - Long Call: Bullish strategy with limited risk
                - Long Put: Bearish strategy with limited risk
                - Covered Call: Income generation on stock positions
                - Cash-Secured Put: Income generation with potential stock acquisition

                Chapter 3: Advanced Options Strategies

                Complex strategies for experienced traders:
                - Iron Condor: Neutral strategy for range-bound markets
                - Butterfly Spreads: Limited risk/reward strategies
                - Calendar Spreads: Time decay strategies
                - Straddles and Strangles: Volatility strategies

                Chapter 4: Options Risk Management

                Managing options positions:
                - Position sizing for options trades
                - Managing early assignment risk
                - Adjusting losing positions
                - Profit-taking strategies
                """

            else:
                # Generic trading content
                return f"""
                Chapter 1: Introduction to {book_title}

                This comprehensive guide covers essential trading concepts and strategies.

                Key topics include:
                - Market analysis techniques
                - Risk management principles
                - Trading psychology
                - Strategy development and backtesting

                Chapter 2: Technical Analysis

                Technical analysis fundamentals:
                - Chart patterns and their significance
                - Technical indicators and oscillators
                - Support and resistance levels
                - Trend analysis and identification

                Chapter 3: Risk Management

                Essential risk management concepts:
                - Position sizing calculations
                - Stop loss placement strategies
                - Portfolio diversification
                - Risk-reward ratio optimization

                Chapter 4: Trading Strategies

                Proven trading strategies:
                - Trend following systems
                - Mean reversion strategies
                - Breakout trading techniques
                - Swing trading approaches
                """

        except Exception as e:
            self.logger.error(f"Error generating book content: {e}")
            return "Trading content placeholder"

    def _generate_topic_content(self, topic: str) -> str:
        """Generate comprehensive content for specific trading topics"""
        try:
            if "Options" in topic:
                return """
                Advanced Options Trading Strategies

                1. Volatility Trading
                - Understanding implied volatility
                - Volatility skew and term structure
                - Trading volatility with straddles and strangles
                - Calendar spreads for time decay

                2. Income Strategies
                - Covered calls for stock enhancement
                - Cash-secured puts for stock acquisition
                - Iron condors for range-bound markets
                - Butterfly spreads for precise targeting

                3. Hedging Strategies
                - Protective puts for downside protection
                - Collar strategies for cost-effective hedging
                - Portfolio hedging with index options
                - Dynamic hedging techniques

                4. Advanced Greeks Management
                - Delta hedging for market neutrality
                - Gamma scalping strategies
                - Theta decay optimization
                - Vega risk management
                """

            elif "Risk Management" in topic:
                return """
                Comprehensive Risk Management in Trading

                1. Position Sizing
                - Kelly Criterion for optimal position sizing
                - Fixed fractional position sizing
                - Volatility-based position sizing
                - Risk parity approaches

                2. Portfolio Risk Management
                - Correlation analysis and diversification
                - Maximum drawdown limits
                - Value at Risk (VaR) calculations
                - Stress testing and scenario analysis

                3. Trade-Level Risk Management
                - Stop loss placement techniques
                - Trailing stop strategies
                - Risk-reward ratio optimization
                - Multiple timeframe risk analysis

                4. Psychological Risk Management
                - Emotional discipline techniques
                - Avoiding revenge trading
                - Managing overconfidence
                - Dealing with losing streaks
                """

            elif "Quantitative" in topic:
                return """
                Quantitative Methods for Traders

                1. Statistical Analysis
                - Backtesting methodologies
                - Statistical significance testing
                - Monte Carlo simulations
                - Walk-forward analysis

                2. Machine Learning in Trading
                - Feature engineering for trading models
                - Supervised learning for price prediction
                - Unsupervised learning for pattern recognition
                - Reinforcement learning for strategy optimization

                3. Portfolio Optimization
                - Modern Portfolio Theory applications
                - Black-Litterman model
                - Risk parity strategies
                - Factor-based investing

                4. Algorithmic Trading
                - Strategy development frameworks
                - Execution algorithms
                - Market microstructure considerations
                - High-frequency trading concepts
                """

            else:
                return f"""
                Comprehensive Guide to {topic}

                This guide provides in-depth coverage of {topic.lower()} concepts and applications.

                Key areas covered:
                - Fundamental principles and theory
                - Practical implementation strategies
                - Real-world case studies and examples
                - Advanced techniques and best practices
                - Risk management considerations
                - Performance measurement and optimization
                """

        except Exception as e:
            self.logger.error(f"Error generating topic content: {e}")
            return f"Content for {topic}"

    def _extract_author_from_title(self, title: str) -> str:
        """Extract author name from book title"""
        if "by " in title:
            return title.split("by ")[-1].strip()
        else:
            # Default authors for known books
            author_mapping = {
                'Trading in the Zone': 'Mark Douglas',
                'Market Wizards': 'Jack Schwager',
                'The New Market Wizards': 'Jack Schwager',
                'Reminiscences of a Stock Operator': 'Edwin Lefèvre',
                'Technical Analysis of the Financial Markets': 'John Murphy',
                'Options as a Strategic Investment': 'Lawrence McMillan',
                'The Options Playbook': 'Brian Overby',
                'Quantitative Trading': 'Ernest Chan',
                'Algorithmic Trading': 'Ernest Chan',
                'The Intelligent Investor': 'Benjamin Graham'
            }

            for book, author in author_mapping.items():
                if book in title:
                    return author

            return "Trading Expert"

    def _initialize_vector_components(self):
        """Initialize vector search components"""
        try:
            # Load or create sentence transformer
            self.encoder = SentenceTransformer('all-MiniLM-L6-v2')

            # Load existing vector index if available
            if os.path.exists(self.vector_db_path) and os.path.exists(self.embeddings_path):
                self.vector_index = faiss.read_index(self.vector_db_path)
                with open(self.embeddings_path, 'rb') as f:
                    self.book_chunks = pickle.load(f)
                self.logger.info(f"Loaded {len(self.book_chunks)} book chunks from vector database")
            else:
                # Create new FAISS index
                self.vector_index = faiss.IndexFlatIP(384)  # 384 is the dimension for all-MiniLM-L6-v2
                self.logger.info("Created new vector database")

        except Exception as e:
            self.logger.error(f"Error initializing vector components: {e}")
            self.encoder = None
            self.vector_index = None

    def add_book_content(self, book_title: str, author: str, content: str, category: str = "trading") -> bool:
        """Add book content to RAG system"""
        try:
            # Split content into chunks
            chunks = self._split_into_chunks(content, book_title)

            # Store in database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Add book metadata
            cursor.execute('''
                INSERT OR REPLACE INTO book_metadata
                (book_title, author, category, description, total_chunks, added_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (book_title, author, category, f"Trading book by {author}", len(chunks), datetime.now().isoformat()))

            # Add chunks
            for chunk in chunks:
                cursor.execute('''
                    INSERT OR REPLACE INTO book_chunks
                    (chunk_id, book_title, chapter, section, content, metadata, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    chunk.chunk_id, chunk.book_title, chunk.chapter, chunk.section,
                    chunk.content, json.dumps(chunk.metadata or {}), datetime.now().isoformat()
                ))

            conn.commit()
            conn.close()

            # Add to vector database if available
            if self.encoder and self.vector_index is not None:
                self._add_chunks_to_vector_db(chunks)

            self.logger.info(f"Added {len(chunks)} chunks from '{book_title}' to RAG system")
            return True

        except Exception as e:
            self.logger.error(f"Error adding book content: {e}")
            return False

    def _split_into_chunks(self, content: str, book_title: str, chunk_size: int = 500) -> List[BookChunk]:
        """Split book content into manageable chunks"""

        chunks = []

        # Split by chapters first (look for chapter markers)
        chapter_pattern = r'(Chapter \d+|CHAPTER \d+|Ch\. \d+)'
        chapters = re.split(chapter_pattern, content)

        current_chapter = "Introduction"

        for i, section in enumerate(chapters):
            if re.match(chapter_pattern, section):
                current_chapter = section
                continue

            # Split long sections into smaller chunks
            words = section.split()

            for j in range(0, len(words), chunk_size):
                chunk_words = words[j:j + chunk_size]
                chunk_content = ' '.join(chunk_words)

                if len(chunk_content.strip()) > 50:  # Only add meaningful chunks
                    chunk_id = hashlib.md5(f"{book_title}_{current_chapter}_{j}".encode()).hexdigest()

                    chunks.append(BookChunk(
                        book_title=book_title,
                        chapter=current_chapter,
                        section=f"Section {j // chunk_size + 1}",
                        content=chunk_content.strip(),
                        chunk_id=chunk_id,
                        metadata={
                            "word_count": len(chunk_words),
                            "chunk_index": j // chunk_size,
                            "chapter_index": i
                        }
                    ))

        return chunks

    def _add_chunks_to_vector_db(self, chunks: List[BookChunk]):
        """Add chunks to vector database"""
        try:
            if not self.encoder or self.vector_index is None:
                return

            # Generate embeddings
            texts = [chunk.content for chunk in chunks]
            embeddings = self.encoder.encode(texts)

            # Add to FAISS index
            self.vector_index.add(embeddings.astype('float32'))

            # Store chunks with embeddings
            for chunk, embedding in zip(chunks, embeddings):
                chunk.embedding = embedding
                self.book_chunks.append(chunk)

            # Save to disk
            faiss.write_index(self.vector_index, self.vector_db_path)
            with open(self.embeddings_path, 'wb') as f:
                pickle.dump(self.book_chunks, f)

        except Exception as e:
            self.logger.error(f"Error adding chunks to vector database: {e}")

    def search_books(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search trading books using vector similarity"""
        try:
            if self.encoder and self.vector_index is not None and len(self.book_chunks) > 0:
                return self._vector_search(query, top_k)
            else:
                return self._text_search(query, top_k)

        except Exception as e:
            self.logger.error(f"Error searching books: {e}")
            return []

    def _vector_search(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """Perform vector similarity search"""
        try:
            # Encode query
            query_embedding = self.encoder.encode([query])

            # Search FAISS index
            scores, indices = self.vector_index.search(query_embedding.astype('float32'), top_k)

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx < len(self.book_chunks):
                    chunk = self.book_chunks[idx]
                    results.append({
                        "book_title": chunk.book_title,
                        "chapter": chunk.chapter,
                        "section": chunk.section,
                        "content": chunk.content,
                        "relevance_score": float(score),
                        "metadata": chunk.metadata
                    })

            return results

        except Exception as e:
            self.logger.error(f"Error in vector search: {e}")
            return []

    def _text_search(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """Fallback text-based search"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Simple text search
            cursor.execute('''
                SELECT book_title, chapter, section, content, metadata
                FROM book_chunks
                WHERE content LIKE ? OR chapter LIKE ?
                ORDER BY
                    CASE
                        WHEN content LIKE ? THEN 1
                        WHEN chapter LIKE ? THEN 2
                        ELSE 3
                    END
                LIMIT ?
            ''', (f'%{query}%', f'%{query}%', f'%{query}%', f'%{query}%', top_k))

            results = []
            for row in cursor.fetchall():
                results.append({
                    "book_title": row[0],
                    "chapter": row[1],
                    "section": row[2],
                    "content": row[3],
                    "relevance_score": 0.5,  # Default score for text search
                    "metadata": json.loads(row[4]) if row[4] else {}
                })

            conn.close()
            return results

        except Exception as e:
            self.logger.error(f"Error in text search: {e}")
            return []

    def get_book_summary(self, book_title: str) -> Dict[str, Any]:
        """Get summary of a specific book"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get book metadata
            cursor.execute('SELECT * FROM book_metadata WHERE book_title = ?', (book_title,))
            metadata = cursor.fetchone()

            if not metadata:
                return {"error": "Book not found"}

            # Get chapter list
            cursor.execute('''
                SELECT DISTINCT chapter FROM book_chunks
                WHERE book_title = ?
                ORDER BY chapter
            ''', (book_title,))
            chapters = [row[0] for row in cursor.fetchall()]

            conn.close()

            return {
                "book_title": metadata[0],
                "author": metadata[1],
                "category": metadata[2],
                "description": metadata[3],
                "total_chunks": metadata[4],
                "chapters": chapters,
                "added_at": metadata[5]
            }

        except Exception as e:
            self.logger.error(f"Error getting book summary: {e}")
            return {"error": str(e)}


@dataclass
class OptionsEducationContext:
    """Options education context"""
    concept: str
    simple_explanation: str
    analogy: str
    warning_conditions: List[str]
    educational_tips: List[str]
    risk_level: str


class TradingBooksRAG:
    """Trading books Retrieval-Augmented Generation system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.book_contents = self._initialize_book_contents()
    
    def _initialize_book_contents(self) -> List[BookContent]:
        """Initialize trading book contents"""
        
        contents = []
        
        # Trading in the Zone - Mark Douglas
        contents.extend([
            BookContent(
                book_title="Trading in the Zone",
                chapter="Chapter 1",
                section="The Psychology of Trading",
                content="""The biggest challenge traders face is not market analysis, but managing their own psychology. Most traders fail because they cannot control their emotions - fear and greed drive their decisions instead of logic and discipline. The market doesn't care about your hopes, fears, or financial needs. It simply moves based on the collective actions of all participants.""",
                concepts=["psychology", "emotions", "discipline", "fear", "greed"]
            ),
            BookContent(
                book_title="Trading in the Zone",
                chapter="Chapter 2",
                section="Probabilistic Thinking",
                content="""Successful trading requires thinking in probabilities, not certainties. Each trade is just one instance in a series of trades with an edge. You don't need to know what will happen next to make money. You need to know that your edge will play out over a series of trades. Think like a casino - they don't know which hand will win, but they know their edge will profit over time.""",
                concepts=["probability", "edge", "uncertainty", "series_of_trades", "casino_mindset"]
            ),
            BookContent(
                book_title="Trading in the Zone",
                chapter="Chapter 3",
                section="Risk Management",
                content="""Risk is not just about how much money you can lose on a trade. Risk is about the possibility of not achieving your trading goals. The best traders predefine their risk before entering any trade. They know exactly how much they're willing to lose and stick to it religiously. This removes the emotional component from trading decisions.""",
                concepts=["risk_management", "predefined_risk", "trading_goals", "emotional_control"]
            )
        ])
        
        # Market Wizards - Jack Schwager
        contents.extend([
            BookContent(
                book_title="Market Wizards",
                chapter="Chapter 1",
                section="Ed Seykota Interview",
                content="""Ed Seykota emphasizes that successful trading is about managing risk, not predicting markets. His key insights: 'The elements of good trading are cutting losses, cutting losses, and cutting losses. If you can follow these three rules, you may have a chance.' He also notes that 'Win or lose, everybody gets what they want out of the market.'""",
                concepts=["cutting_losses", "risk_management", "market_psychology", "discipline"]
            ),
            BookContent(
                book_title="Market Wizards",
                chapter="Chapter 2",
                section="Michael Marcus Interview",
                content="""Michael Marcus learned that you have to be willing to make mistakes regularly; there is nothing wrong with it. The key is to recognize mistakes quickly and cut losses short. He emphasizes the importance of position sizing: 'I never risk more than 1-2% of my total equity on any trade.' This allows him to survive the inevitable losing streaks.""",
                concepts=["mistakes", "position_sizing", "cutting_losses", "equity_management"]
            )
        ])
        
        # Reminiscences of a Stock Operator
        contents.extend([
            BookContent(
                book_title="Reminiscences of a Stock Operator",
                chapter="Chapter 4",
                section="The Big Money",
                content="""It was never my thinking that made the big money for me. It always was my sitting. Got that? My sitting tight! Men who can both be right and sit tight are uncommon. The market does not beat them. They beat themselves, because though they have brains they cannot sit still in the money-making position.""",
                concepts=["patience", "sitting_tight", "position_holding", "self_discipline"]
            ),
            BookContent(
                book_title="Reminiscences of a Stock Operator",
                chapter="Chapter 6",
                section="Reading the Tape",
                content="""The tape tells the story if you know how to read it. Price and volume action reveals the true intentions of the big players. When a stock acts right, it will continue to act right. When it acts wrong, get out immediately. The market will tell you when you're wrong - listen to it.""",
                concepts=["tape_reading", "price_action", "volume_analysis", "market_signals"]
            )
        ])
        
        # The Intelligent Investor
        contents.extend([
            BookContent(
                book_title="The Intelligent Investor",
                chapter="Chapter 8",
                section="Mr. Market",
                content="""Imagine that in some private business you own a small share that cost you $1,000. One of your partners, named Mr. Market, is very obliging indeed. Every day he tells you what he thinks your interest is worth and offers either to buy you out or to sell you an additional interest on that basis. The intelligent investor will use Mr. Market's mood swings to their advantage, buying when he's pessimistic and selling when he's euphoric.""",
                concepts=["mr_market", "market_psychology", "contrarian_thinking", "value_investing"]
            )
        ])
        
        return contents
    
    def search_content(self, query: str, book_filter: Optional[str] = None) -> List[BookContent]:
        """Search book contents for relevant information"""
        
        query_lower = query.lower()
        relevant_contents = []
        
        for content in self.book_contents:
            # Filter by book if specified
            if book_filter and book_filter.lower() not in content.book_title.lower():
                continue
            
            # Search in content text and concepts
            content_text = content.content.lower()
            concepts_text = ' '.join(content.concepts).lower()
            
            # Calculate relevance score
            relevance_score = 0
            
            # Direct text matches
            if query_lower in content_text:
                relevance_score += 3
            
            # Concept matches
            for concept in content.concepts:
                if concept in query_lower or query_lower in concept:
                    relevance_score += 2
            
            # Keyword matches
            query_words = query_lower.split()
            for word in query_words:
                if len(word) > 3:  # Skip short words
                    if word in content_text:
                        relevance_score += 1
                    if word in concepts_text:
                        relevance_score += 1
            
            if relevance_score > 0:
                relevant_contents.append((content, relevance_score))
        
        # Sort by relevance and return top results
        relevant_contents.sort(key=lambda x: x[1], reverse=True)
        return [content for content, score in relevant_contents[:5]]
    
    def get_educational_response(self, query: EducationQuery) -> EducationResponse:
        """Generate educational response from book contents"""
        
        try:
            # Search for relevant content
            relevant_contents = self.search_content(query.question, query.book_filter)
            
            if not relevant_contents:
                return EducationResponse(
                    answer="I couldn't find specific information about that topic in the trading books. Could you rephrase your question or ask about trading psychology, risk management, or market analysis?",
                    sources=[],
                    book_references=[],
                    confidence=0.1,
                    related_concepts=[]
                )
            
            # Generate comprehensive answer
            answer_parts = []
            sources = []
            book_references = []
            all_concepts = []
            
            for content in relevant_contents:
                answer_parts.append(f"From '{content.book_title}': {content.content}")
                sources.append(f"{content.book_title} - {content.chapter} - {content.section}")
                book_references.append(content.book_title)
                all_concepts.extend(content.concepts)
            
            # Combine answer
            full_answer = "\n\n".join(answer_parts)
            
            # Add educational summary
            full_answer += f"\n\n💡 Key takeaway: {self._generate_key_takeaway(query.question, relevant_contents)}"
            
            return EducationResponse(
                answer=full_answer,
                sources=sources,
                book_references=list(set(book_references)),
                confidence=min(0.9, len(relevant_contents) * 0.2),
                related_concepts=list(set(all_concepts))
            )
            
        except Exception as e:
            self.logger.error(f"Error generating educational response: {e}")
            return EducationResponse(
                answer="I encountered an error while searching the trading books. Please try asking your question differently.",
                sources=[],
                book_references=[],
                confidence=0.0,
                related_concepts=[]
            )
    
    def _generate_key_takeaway(self, question: str, contents: List[BookContent]) -> str:
        """Generate key takeaway from the content"""
        
        question_lower = question.lower()
        
        if any(word in question_lower for word in ['risk', 'loss', 'stop']):
            return "Risk management is the foundation of successful trading. Always define your risk before entering any trade."
        
        elif any(word in question_lower for word in ['psychology', 'emotion', 'fear', 'greed']):
            return "Trading psychology is often more important than market analysis. Master your emotions to master the markets."
        
        elif any(word in question_lower for word in ['position', 'size', 'money']):
            return "Position sizing determines your long-term survival. Never risk more than you can afford to lose on any single trade."
        
        elif any(word in question_lower for word in ['patience', 'wait', 'timing']):
            return "Patience is a trader's greatest virtue. The best trades often require waiting for the right setup."
        
        else:
            return "Successful trading combines technical analysis, risk management, and psychological discipline."


class EnhancedMemorySystem:
    """Enhanced memory system for learning and preferences"""
    
    def __init__(self, db_path: str = "atlas_memory.db"):
        self.logger = logging.getLogger(__name__)
        self.db_path = db_path
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize SQLite database for memory storage"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS memory_entries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    memory_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    metadata TEXT,
                    timestamp TEXT NOT NULL,
                    importance_score REAL DEFAULT 0.5
                )
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_session_id ON memory_entries(session_id)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_memory_type ON memory_entries(memory_type)
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error initializing memory database: {e}")
    
    def start_session(self) -> str:
        """Start a new conversation session"""
        session_id = f"session_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{hash(datetime.utcnow()) % 10000}"
        
        self.store_memory(
            session_id=session_id,
            memory_type="session",
            content="Session started",
            metadata={"start_time": datetime.utcnow().isoformat()},
            importance_score=0.3
        )
        
        return session_id
    
    def store_memory(self, session_id: str, memory_type: str, content: str,
                    metadata: Optional[Dict[str, Any]] = None, importance_score: float = 0.5):
        """Store memory entry"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO memory_entries (session_id, memory_type, content, metadata, timestamp, importance_score)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                session_id,
                memory_type,
                content,
                json.dumps(metadata or {}),
                datetime.utcnow().isoformat(),
                importance_score
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error storing memory: {e}")
    
    def retrieve_memories(self, session_id: str, memory_type: Optional[str] = None,
                         limit: int = 50) -> List[MemoryEntry]:
        """Retrieve memories for session"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if memory_type:
                cursor.execute('''
                    SELECT session_id, memory_type, content, metadata, timestamp, importance_score
                    FROM memory_entries
                    WHERE session_id = ? AND memory_type = ?
                    ORDER BY timestamp DESC
                    LIMIT ?
                ''', (session_id, memory_type, limit))
            else:
                cursor.execute('''
                    SELECT session_id, memory_type, content, metadata, timestamp, importance_score
                    FROM memory_entries
                    WHERE session_id = ?
                    ORDER BY timestamp DESC
                    LIMIT ?
                ''', (session_id, limit))
            
            rows = cursor.fetchall()
            conn.close()
            
            memories = []
            for row in rows:
                memories.append(MemoryEntry(
                    session_id=row[0],
                    memory_type=row[1],
                    content=row[2],
                    metadata=json.loads(row[3]),
                    timestamp=datetime.fromisoformat(row[4]),
                    importance_score=row[5]
                ))
            
            return memories
            
        except Exception as e:
            self.logger.error(f"Error retrieving memories: {e}")
            return []
    
    def store_learning_progress(self, session_id: str, concept: str, 
                              understanding_level: float, notes: str = ""):
        """Store learning progress for a concept"""
        
        metadata = {
            "concept": concept,
            "understanding_level": understanding_level,
            "notes": notes
        }
        
        self.store_memory(
            session_id=session_id,
            memory_type="learning",
            content=f"Learning progress: {concept}",
            metadata=metadata,
            importance_score=0.8
        )
    
    def get_learning_progress(self, session_id: str) -> Dict[str, Any]:
        """Get learning progress summary"""
        
        learning_memories = self.retrieve_memories(session_id, "learning")
        
        progress = {}
        for memory in learning_memories:
            concept = memory.metadata.get("concept", "unknown")
            understanding = memory.metadata.get("understanding_level", 0.0)
            
            if concept not in progress or understanding > progress[concept]["level"]:
                progress[concept] = {
                    "level": understanding,
                    "last_updated": memory.timestamp.isoformat(),
                    "notes": memory.metadata.get("notes", "")
                }
        
        return progress


class OptionsEducationEngine:
    """Options trading education and Greeks explanation"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.education_contexts = self._initialize_options_education()
    
    def _initialize_options_education(self) -> Dict[str, OptionsEducationContext]:
        """Initialize options education contexts"""
        
        contexts = {}
        
        # Theta Decay
        contexts["theta_decay"] = OptionsEducationContext(
            concept="Theta Decay",
            simple_explanation="Theta measures how much an option's price decreases each day as it gets closer to expiration. Options lose value over time, and this loss accelerates as expiration approaches.",
            analogy="Think of theta like ice cream melting in the sun. The closer you get to a hot day (expiration), the faster it melts (loses value). Weekend and holidays are like putting the ice cream in a freezer - time passes but no melting occurs.",
            warning_conditions=[
                "Buying options close to expiration",
                "Holding long options through weekends",
                "Not accounting for time decay in strategy"
            ],
            educational_tips=[
                "Theta decay accelerates in the final 30 days before expiration",
                "Options sellers benefit from theta decay",
                "Consider selling options instead of buying them to profit from time decay"
            ],
            risk_level="moderate"
        )
        
        # IV Crush
        contexts["iv_crush"] = OptionsEducationContext(
            concept="Implied Volatility Crush",
            simple_explanation="IV crush happens when implied volatility drops suddenly, causing option prices to fall even if the stock price moves in your favor. This often occurs after earnings announcements.",
            analogy="Imagine you're selling umbrellas when everyone expects a storm (high IV). If the weather forecast changes to sunny (low IV), nobody wants umbrellas anymore, even if it's still cloudy outside.",
            warning_conditions=[
                "Buying options before earnings",
                "High implied volatility levels",
                "Major events or announcements pending"
            ],
            educational_tips=[
                "Check implied volatility percentile before buying options",
                "Consider selling options when IV is high",
                "Be aware of upcoming events that could cause IV crush"
            ],
            risk_level="high"
        )
        
        # The Greeks
        contexts["greeks"] = OptionsEducationContext(
            concept="The Greeks",
            simple_explanation="The Greeks measure how option prices change with different factors: Delta (stock price), Gamma (delta changes), Theta (time), Vega (volatility), and Rho (interest rates).",
            analogy="Think of the Greeks like a car's dashboard. Delta is your speedometer (how fast you're moving with the stock), Theta is your fuel gauge (time running out), Vega is your weather report (volatility conditions), and Gamma is your acceleration (how quickly things change).",
            warning_conditions=[
                "Ignoring gamma risk on short options",
                "Not understanding delta hedging",
                "Overlooking vega risk in volatile markets"
            ],
            educational_tips=[
                "Delta tells you how much the option price moves per $1 stock move",
                "High gamma means delta changes quickly - can be dangerous for sellers",
                "Vega risk is highest for at-the-money options with time remaining"
            ],
            risk_level="moderate"
        )
        
        return contexts
    
    def get_education_context(self, concept: str) -> Optional[OptionsEducationContext]:
        """Get education context for options concept"""
        return self.education_contexts.get(concept.lower().replace(" ", "_"))
    
    def explain_options_concept(self, concept: str, user_level: str = "beginner") -> str:
        """Explain options concept based on user level"""
        
        context = self.get_education_context(concept)
        if not context:
            return f"I don't have specific educational content for '{concept}'. Try asking about theta decay, IV crush, or the Greeks."
        
        explanation = f"📚 **{context.concept}**\n\n"
        explanation += f"**Simple Explanation:** {context.simple_explanation}\n\n"
        explanation += f"**Analogy:** {context.analogy}\n\n"
        
        if context.warning_conditions:
            explanation += "⚠️ **Warning Signs:**\n"
            for warning in context.warning_conditions:
                explanation += f"• {warning}\n"
            explanation += "\n"
        
        if context.educational_tips:
            explanation += "💡 **Key Tips:**\n"
            for tip in context.educational_tips:
                explanation += f"• {tip}\n"
            explanation += "\n"
        
        explanation += f"**Risk Level:** {context.risk_level.title()}\n\n"
        
        if user_level == "beginner":
            explanation += "🎓 **For Beginners:** Start with paper trading options to understand these concepts without risking real money. Focus on learning one Greek at a time."
        
        return explanation


class AtlasEducationEngine:
    """Main education engine coordinating all educational functionality"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.books_rag = TradingBooksRAG()
        self.memory_system = EnhancedMemorySystem()
        self.options_education = OptionsEducationEngine()

        # Initialize with default trading books content
        self._initialize_default_books()
    
    def process_educational_query(self, query: str, session_id: str, 
                                user_level: str = "beginner") -> Dict[str, Any]:
        """Process educational query with full pipeline"""
        
        try:
            # Store the query in memory
            self.memory_system.store_memory(
                session_id=session_id,
                memory_type="education_query",
                content=query,
                metadata={"user_level": user_level},
                importance_score=0.6
            )
            
            # Check if it's an options-related query
            options_keywords = ["option", "theta", "delta", "gamma", "vega", "rho", "iv", "implied volatility", "greeks"]
            if any(keyword in query.lower() for keyword in options_keywords):
                # Extract concept from query
                concept = self._extract_options_concept(query)
                if concept:
                    explanation = self.options_education.explain_options_concept(concept, user_level)
                    
                    # Store learning progress
                    self.memory_system.store_learning_progress(
                        session_id=session_id,
                        concept=concept,
                        understanding_level=0.5,  # Initial understanding
                        notes=f"Asked about {concept}"
                    )
                    
                    return {
                        "type": "options_education",
                        "response": explanation,
                        "concept": concept,
                        "source": "options_education_engine"
                    }
            
            # Use books RAG for general trading education
            education_query = EducationQuery(question=query)
            rag_response = self.books_rag.get_educational_response(education_query)
            
            # Store learning progress for concepts found
            for concept in rag_response.related_concepts:
                self.memory_system.store_learning_progress(
                    session_id=session_id,
                    concept=concept,
                    understanding_level=0.4,
                    notes=f"Learned about {concept} from books"
                )
            
            return {
                "type": "books_education",
                "response": rag_response.answer,
                "sources": rag_response.sources,
                "book_references": rag_response.book_references,
                "confidence": rag_response.confidence,
                "related_concepts": rag_response.related_concepts
            }
            
        except Exception as e:
            self.logger.error(f"Error processing educational query: {e}")
            return {
                "type": "error",
                "response": "I encountered an error while processing your educational query. Please try rephrasing your question.",
                "error": str(e)
            }
    
    def _extract_options_concept(self, query: str) -> Optional[str]:
        """Extract options concept from query"""
        
        query_lower = query.lower()
        
        if any(word in query_lower for word in ["theta", "time decay"]):
            return "theta_decay"
        elif any(word in query_lower for word in ["iv", "implied volatility", "volatility crush"]):
            return "iv_crush"
        elif any(word in query_lower for word in ["greeks", "delta", "gamma", "vega", "rho"]):
            return "greeks"
        
        return None
    
    def get_latest_trading_goal(self, user_id: str):
        """Get user's latest active trading goal"""
        try:
            # Check if memory system has the method (it's in the AI engine's context memory)
            if hasattr(self.memory_system, 'get_active_goal'):
                return self.memory_system.get_active_goal(user_id)
            else:
                # Fallback: search memory entries for trading goals
                memories = self.memory_system.retrieve_memories(user_id, "trading_goal", limit=1)
                if memories:
                    # Parse the goal from metadata
                    metadata = memories[0].metadata
                    from atlas_ai_engine import TradingGoal
                    return TradingGoal(
                        goal_type=metadata.get('goal_type', 'learning'),
                        target_amount=metadata.get('target_amount'),
                        timeframe=metadata.get('timeframe'),
                        risk_tolerance=metadata.get('risk_tolerance', 'moderate'),
                        created_at=memories[0].timestamp
                    )
                return None
        except Exception as e:
            self.logger.error(f"Error getting latest trading goal: {e}")
            return None

    def get_learning_summary(self, session_id: str) -> Dict[str, Any]:
        """Get learning progress summary for session"""

        try:
            # Get learning progress
            progress = self.memory_system.get_learning_progress(session_id)

            # Get recent educational queries
            education_memories = self.memory_system.retrieve_memories(session_id, "education_query", limit=10)

            # Calculate overall learning score
            if progress:
                avg_understanding = sum(p["level"] for p in progress.values()) / len(progress)
                concepts_learned = len(progress)
            else:
                avg_understanding = 0.0
                concepts_learned = 0
            
            return {
                "session_id": session_id,
                "concepts_learned": concepts_learned,
                "average_understanding": avg_understanding,
                "progress_by_concept": progress,
                "recent_queries": [
                    {
                        "query": memory.content,
                        "timestamp": memory.timestamp.isoformat(),
                        "user_level": memory.metadata.get("user_level", "unknown")
                    }
                    for memory in education_memories
                ],
                "recommendations": self._generate_learning_recommendations(progress, avg_understanding)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting learning summary: {e}")
            return {
                "error": str(e),
                "session_id": session_id
            }
    
    def _generate_learning_recommendations(self, progress: Dict[str, Any], 
                                         avg_understanding: float) -> List[str]:
        """Generate learning recommendations"""
        
        recommendations = []
        
        if avg_understanding < 0.3:
            recommendations.append("Focus on basic trading concepts like risk management and position sizing")
            recommendations.append("Read 'Trading in the Zone' for psychology fundamentals")
        
        elif avg_understanding < 0.6:
            recommendations.append("Practice with paper trading to reinforce concepts")
            recommendations.append("Study technical analysis and chart patterns")
        
        else:
            recommendations.append("Consider advanced topics like options strategies")
            recommendations.append("Focus on developing your own trading system")
        
        # Specific concept recommendations
        if "risk_management" not in progress:
            recommendations.append("Learn about risk management - it's the foundation of successful trading")
        
        if "psychology" not in progress:
            recommendations.append("Study trading psychology to understand emotional control")
        
        return recommendations

    def _initialize_default_books(self):
        """Initialize with default trading book content"""

        default_books = {
            "Trading in the Zone": {
                "author": "Mark Douglas",
                "content": """
                Chapter 1: The Psychology of Trading

                The biggest challenge traders face is not market analysis, but managing their own psychology.
                Most traders focus on finding the perfect system or indicator, but the real edge comes from
                developing the right mindset.

                The five fundamental truths about trading:
                1. Anything can happen in the market
                2. You don't need to know what's going to happen next to make money
                3. There is a random distribution between wins and losses for any given set of variables
                4. An edge is nothing more than an indication of a higher probability of one outcome over another
                5. Every moment in the market is unique

                Chapter 2: Risk Management

                The best traders predefine their risk before entering any trade. They never risk more than they
                can afford to lose, and they always have a plan for when things go wrong.

                Risk management is not just about stop losses - it's about position sizing, diversification,
                and understanding that losses are part of the business.

                Chapter 3: Developing Consistency

                Consistency comes from following a well-defined process, not from trying to predict the market.
                The most successful traders have learned to think in probabilities rather than certainties.
                """
            },
            "Market Wizards": {
                "author": "Jack Schwager",
                "content": """
                Chapter 1: Ed Seykota - The Elements of Good Trading

                "The elements of good trading are cutting losses, cutting losses, and cutting losses.
                If you can follow these three rules, you may have a chance."

                Ed Seykota emphasizes the importance of risk management above all else. He believes that
                the key to successful trading is not being right all the time, but managing your losses
                when you're wrong.

                Chapter 2: Michael Marcus - The Art of Contrary Thinking

                "I never risk more than 1-2% of my total equity on any trade. The secret to being successful
                from a trading perspective is to have an indefatigable and an undying and uncompromising
                commitment to getting the right answer."

                Marcus stresses the importance of position sizing and risk management. He also emphasizes
                the value of being contrarian when everyone else is thinking the same way.

                Chapter 3: Bruce Kovner - The Importance of Fundamentals

                "Michael taught me one other thing that is absolutely critical: You have to be willing to
                make mistakes regularly; there is nothing wrong with it. Michael taught me about making
                your best judgment, being wrong, making your next best judgment, being wrong, making your
                third best judgment, and doubling your money."
                """
            },
            "Options Trading Essentials": {
                "author": "A.T.L.A.S Education Team",
                "content": """
                Chapter 1: Introduction to Options

                Options are financial contracts that give you the right, but not the obligation, to buy or
                sell an underlying asset at a specific price within a certain time period.

                Call Options: Give you the right to BUY the underlying asset
                Put Options: Give you the right to SELL the underlying asset

                Chapter 2: The Greeks

                Delta: Measures how much the option price changes for every $1 move in the underlying stock
                Gamma: Measures how much delta changes as the stock price moves
                Theta: Measures time decay - how much value the option loses each day
                Vega: Measures sensitivity to changes in implied volatility

                Chapter 3: Basic Strategies

                Long Call: Bullish strategy - buy a call option
                Long Put: Bearish strategy - buy a put option
                Covered Call: Own stock + sell call option for income
                Cash-Secured Put: Sell put option while holding cash to buy stock if assigned

                Chapter 4: Risk Management in Options

                Never risk more than you can afford to lose. Options can expire worthless, so position
                sizing is crucial. Always have a plan for both profit-taking and loss-cutting.
                """
            }
        }

        # Add default books to RAG system
        for book_title, book_data in default_books.items():
            try:
                success = self.books_rag.add_book_content(
                    book_title=book_title,
                    author=book_data["author"],
                    content=book_data["content"],
                    category="trading"
                )
                if success:
                    self.logger.info(f"Added default book: {book_title}")
                else:
                    self.logger.warning(f"Failed to add default book: {book_title}")
            except Exception as e:
                self.logger.error(f"Error adding default book {book_title}: {e}")

    def search_trading_books(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """Search trading books using advanced RAG"""
        try:
            results = self.books_rag.search_books(query, top_k)

            # Enhance results with educational context
            enhanced_results = []
            for result in results:
                enhanced_result = {
                    **result,
                    "educational_context": self._add_educational_context(result["content"], query),
                    "difficulty_level": self._assess_difficulty_level(result["content"]),
                    "key_concepts": self._extract_key_concepts(result["content"])
                }
                enhanced_results.append(enhanced_result)

            return enhanced_results

        except Exception as e:
            self.logger.error(f"Error searching trading books: {e}")
            return []

    def _add_educational_context(self, content: str, query: str) -> str:
        """Add educational context to search results"""

        # Identify the main topic
        content_lower = content.lower()
        query_lower = query.lower()

        context = ""

        if "risk" in query_lower or "risk" in content_lower:
            context = "💡 **Educational Note**: Risk management is the foundation of successful trading. "
            context += "Professional traders focus more on managing losses than maximizing profits."

        elif "psychology" in query_lower or "emotion" in query_lower:
            context = "🧠 **Trading Psychology**: Emotional control is crucial for trading success. "
            context += "Most trading failures come from psychological mistakes, not analytical errors."

        elif "option" in query_lower or "greek" in content_lower:
            context = "📊 **Options Education**: Options provide leverage and flexibility but require "
            context += "understanding of time decay and volatility. Start with basic strategies."

        elif "position siz" in content_lower or "money management" in content_lower:
            context = "💰 **Position Sizing**: Never risk more than 1-2% of your account on a single trade. "
            context += "Position sizing is more important than entry timing."

        return context

    def _assess_difficulty_level(self, content: str) -> str:
        """Assess the difficulty level of content"""

        content_lower = content.lower()

        # Advanced concepts
        advanced_terms = ["gamma", "vega", "theta", "implied volatility", "black-scholes",
                         "arbitrage", "hedge ratio", "correlation"]

        # Intermediate concepts
        intermediate_terms = ["technical analysis", "support", "resistance", "moving average",
                            "rsi", "macd", "options", "spreads"]

        # Beginner concepts
        beginner_terms = ["buy", "sell", "stock", "price", "profit", "loss", "risk"]

        advanced_count = sum(1 for term in advanced_terms if term in content_lower)
        intermediate_count = sum(1 for term in intermediate_terms if term in content_lower)
        beginner_count = sum(1 for term in beginner_terms if term in content_lower)

        if advanced_count >= 2:
            return "advanced"
        elif intermediate_count >= 2:
            return "intermediate"
        else:
            return "beginner"

    def _extract_key_concepts(self, content: str) -> List[str]:
        """Extract key trading concepts from content"""

        content_lower = content.lower()

        concepts = []

        concept_keywords = {
            "risk_management": ["risk", "stop loss", "position siz", "money management"],
            "psychology": ["psychology", "emotion", "discipline", "mindset", "fear", "greed"],
            "technical_analysis": ["technical", "chart", "indicator", "pattern", "trend"],
            "options": ["option", "call", "put", "strike", "expiration", "greek"],
            "fundamentals": ["fundamental", "earnings", "revenue", "valuation", "p/e"],
            "strategy": ["strategy", "system", "method", "approach", "technique"]
        }

        for concept, keywords in concept_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                concepts.append(concept)

        return concepts
